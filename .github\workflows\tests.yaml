name: pefile tests

on: push

jobs:
  tests:
    name: "Python ${{ matrix.python-version }} on ${{ matrix.os }}"
    runs-on: "${{ matrix.os }}"

    strategy:
      fail-fast: false
      matrix:
        os:
          - ubuntu-latest
          - macos-latest
          - windows-latest
        python-version:
          - "3.8"
          - "3.9"
          - "3.10"
          - "3.11"
          - "3.12"
          - "pypy3.9"
        include:
          - os: ubuntu-latest
            python-version: "3.10"
            coverage: 1

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@63c24ba6bd7ba022e95695ff85de572c04a18142 # v2.7.0
        with:
          egress-policy: audit

      - uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@82c7e631bb3cdc910f68e0081d67478d79c6982d # v5.1.0
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install flake8 tox tox-gh-actions
      - name: Lint with flake8
        run: |
          # stop the build if there are Python syntax errors or undefined names
          flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
          # exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
          flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

      - name: Clone https://github.com/erocarrera/pefile-tests
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
        with:
          repository: erocarrera/pefile-tests
          path: ./pefile-tests

      - name: "Run tox for ${{ matrix.python-version }}"
        run: |
          python -m tox

      - if: ${{matrix.coverage}}
        name: "Upload coverage data"
        uses: actions/upload-artifact@1746f4ab65b179e0ea60a494b83293b640dd5bba # v4.3.2
        with:
          name: covdata
          path: .coverage.*

  coverage:
    name: Coverage
    needs: tests
    runs-on: ubuntu-latest
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@63c24ba6bd7ba022e95695ff85de572c04a18142 # v2.7.0
        with:
          egress-policy: audit

      - name: "Check out the repo"
        uses: "actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11"  # v4.1.1

      - name: "Set up Python"
        uses: "actions/setup-python@82c7e631bb3cdc910f68e0081d67478d79c6982d"  # v5.1.0
        with:
          python-version: "3.10"

      - name: "Install dependencies"
        run: |
          python -m pip install --upgrade pip
          python -m pip install tox tox-gh-actions

      - name: "Download coverage data"
        uses: actions/download-artifact@8caf195ad4b1dee92908e23f56eeb0696f1dd42d # v4.1.5
        with:
          name: covdata

      - name: "Combine"
        run: |
          python -m tox -e coverage
          export TOTAL=$(python -c "import json;print(json.load(open('coverage.json'))['totals']['percent_covered_display'])")
          echo "total=$TOTAL" >> $GITHUB_ENV
          echo "### Total coverage: ${TOTAL}%" >> $GITHUB_STEP_SUMMARY

      - name: "Make badge"
        uses: schneegans/dynamic-badges-action@v1.7.0
        with:
          # GIST_TOKEN is a GitHub personal access token with scope "gist".
          auth: ${{ secrets.GIST_TOKEN }}
          gistID: 2150adbc4ea8c61e381fdb9da0943723   # Gist id.
          filename: covbadge.json
          label: Coverage
          message: ${{ env.total }}%
          minColorRange: 50
          maxColorRange: 90
          valColorRange: ${{ env.total }}
