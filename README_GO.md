# pefile-go

A Go port of the popular Python pefile library for parsing Portable Executable (PE) files.

## Overview

This library provides comprehensive support for parsing and analyzing PE files, including:

- DOS and NT header parsing
- Section analysis
- Import/Export table processing  
- Resource extraction
- Debug information
- Digital signature verification
- Hash calculations (imphash, exphash)
- Packer detection utilities

## Installation

```bash
go get github.com/pefile-go/pefile
```

## Quick Start

```go
package main

import (
    "fmt"
    "log"
    
    "github.com/pefile-go/pefile/pefile"
)

func main() {
    pe, err := pefile.NewPE("example.exe")
    if err != nil {
        log.Fatal(err)
    }
    defer pe.Close()
    
    fmt.Printf("Machine: %s\n", pe.FileHeader.Machine)
    fmt.Printf("Sections: %d\n", len(pe.Sections))
    
    // Print imports
    for _, imp := range pe.Imports {
        fmt.Printf("DLL: %s\n", imp.DLL)
        for _, symbol := range imp.Symbols {
            fmt.Printf("  %s\n", symbol.Name)
        }
    }
}
```

## Features

### Core Functionality
- ✅ PE header parsing (DOS, NT, File, Optional headers)
- ✅ Section parsing and analysis
- ✅ Import table processing
- ✅ Export table processing
- ✅ Resource directory parsing
- ✅ Debug directory parsing
- ✅ Base relocation parsing
- ✅ TLS directory parsing
- ✅ Load config directory parsing
- ✅ Delay import parsing
- ✅ Bound import parsing

### Utilities
- ✅ Hash calculations (MD5, SHA1, SHA256, imphash, exphash)
- ✅ Signature database support
- ✅ Packer detection
- ✅ Ordinal lookup for common DLLs
- ✅ PE validation and suspicious file detection

### Compatibility
- Maintains API compatibility with Python pefile where possible
- Handles malformed and malicious PE files gracefully
- Supports both 32-bit and 64-bit PE files
- Cross-platform support (Windows, Linux, macOS)

## Documentation

See the [API documentation](https://pkg.go.dev/github.com/pefile-go/pefile) for detailed usage information.

## License

MIT License - see LICENSE file for details.

## Credits

This is a Go port of the excellent Python pefile library by Ero Carrera.
Original Python version: https://github.com/erocarrera/pefile
