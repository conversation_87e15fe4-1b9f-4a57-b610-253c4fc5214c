package main

import (
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/pefile-go/pefile/pefile"
)

func main() {
	var (
		showImports         = flag.Bool("imports", false, "Show import table")
		showExports         = flag.Bool("exports", false, "Show export table")
		showSections        = flag.Bool("sections", false, "Show section headers")
		showHeaders         = flag.Bool("headers", false, "Show PE headers")
		showResources       = flag.Bool("resources", false, "Show resources")
		showRelocations     = flag.Bool("relocations", false, "Show relocations")
		showTLS             = flag.Bool("tls", false, "Show TLS directory")
		showDebug           = flag.Bool("debug", false, "Show debug directories")
		showHashes          = flag.Bool("hashes", false, "Show file hashes")
		showValidation      = flag.Bool("validate", false, "Validate PE file")
		showPerformance     = flag.Bool("perf", false, "Show performance statistics")
		enableOptimizations = flag.Bool("optimize", false, "Enable performance optimizations")
		showAll             = flag.Bool("all", false, "Show all information")
		verbose             = flag.Bool("v", false, "Verbose output")
		help                = flag.Bool("h", false, "Show help")
	)

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "Usage: %s [options] <pe-file>\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "\nOptions:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -imports example.exe\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -all example.dll\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -headers -sections example.sys\n", os.Args[0])
	}

	flag.Parse()

	if *help {
		flag.Usage()
		return
	}

	if flag.NArg() != 1 {
		fmt.Fprintf(os.Stderr, "Error: Please specify exactly one PE file\n\n")
		flag.Usage()
		os.Exit(1)
	}

	filename := flag.Arg(0)

	// Check if file exists
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		fmt.Fprintf(os.Stderr, "Error: File '%s' does not exist\n", filename)
		os.Exit(1)
	}

	// Parse the PE file
	pe, err := pefile.NewPE(filename)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error parsing PE file: %v\n", err)
		os.Exit(1)
	}
	defer pe.Close()

	// Show warnings if verbose
	if *verbose {
		warnings := pe.GetWarnings()
		if len(warnings) > 0 {
			fmt.Printf("Warnings:\n")
			for _, warning := range warnings {
				fmt.Printf("  %s\n", warning)
			}
			fmt.Println()
		}
	}

	// Determine what to show
	showEverything := *showAll || (!*showImports && !*showExports && !*showSections && !*showHeaders)

	// Show basic file information
	fmt.Printf("PE File Analysis: %s\n", filepath.Base(filename))
	fmt.Printf("File size: %d bytes\n", pe.Size())
	fmt.Printf("Architecture: %s\n", getArchitecture(pe))
	fmt.Printf("File type: %s\n", getFileType(pe))
	fmt.Println()

	// Show headers
	if *showHeaders || showEverything {
		showPEHeaders(pe)
	}

	// Show sections
	if *showSections || showEverything {
		showPESections(pe)
	}

	// Show imports
	if *showImports || showEverything {
		showPEImports(pe)
	}

	// Show exports
	if *showExports || showEverything {
		showPEExports(pe)
	}
}

func getArchitecture(pe *pefile.PE) string {
	if pe.Is64Bit() {
		return "64-bit"
	} else if pe.Is32Bit() {
		return "32-bit"
	}
	return "Unknown"
}

func getFileType(pe *pefile.PE) string {
	if pe.IsDLL() {
		return "Dynamic Link Library (DLL)"
	} else if pe.IsExecutable() {
		return "Executable (EXE)"
	}
	return "Unknown"
}

func showPEHeaders(pe *pefile.PE) {
	fmt.Printf("=== PE Headers ===\n")

	if pe.FileHeader != nil {
		fmt.Printf("Machine: %s (0x%04X)\n",
			pefile.MachineTypeNames[pe.FileHeader.Machine],
			pe.FileHeader.Machine)
		fmt.Printf("Number of sections: %d\n", pe.FileHeader.NumberOfSections)
		fmt.Printf("Time/Date stamp: 0x%08X\n", pe.FileHeader.TimeDateStamp)
		fmt.Printf("Characteristics: 0x%04X\n", pe.FileHeader.Characteristics)

		// Show characteristics flags
		fmt.Printf("  Characteristics flags:\n")
		for flag, name := range pefile.ImageCharacteristicNames {
			if pe.FileHeader.Characteristics&flag != 0 {
				fmt.Printf("    %s\n", name)
			}
		}
	}

	if pe.OptionalHeader32 != nil {
		fmt.Printf("Entry point: 0x%08X\n", pe.OptionalHeader32.AddressOfEntryPoint)
		fmt.Printf("Image base: 0x%08X\n", pe.OptionalHeader32.ImageBase)
		fmt.Printf("Section alignment: 0x%08X\n", pe.OptionalHeader32.SectionAlignment)
		fmt.Printf("File alignment: 0x%08X\n", pe.OptionalHeader32.FileAlignment)
		fmt.Printf("Subsystem: %s (0x%04X)\n",
			pefile.SubsystemTypeNames[pe.OptionalHeader32.Subsystem],
			pe.OptionalHeader32.Subsystem)
	} else if pe.OptionalHeader64 != nil {
		fmt.Printf("Entry point: 0x%08X\n", pe.OptionalHeader64.AddressOfEntryPoint)
		fmt.Printf("Image base: 0x%016X\n", pe.OptionalHeader64.ImageBase)
		fmt.Printf("Section alignment: 0x%08X\n", pe.OptionalHeader64.SectionAlignment)
		fmt.Printf("File alignment: 0x%08X\n", pe.OptionalHeader64.FileAlignment)
		fmt.Printf("Subsystem: %s (0x%04X)\n",
			pefile.SubsystemTypeNames[pe.OptionalHeader64.Subsystem],
			pe.OptionalHeader64.Subsystem)
	}

	fmt.Println()
}

func showPESections(pe *pefile.PE) {
	fmt.Printf("=== Sections ===\n")

	if len(pe.Sections) == 0 {
		fmt.Printf("No sections found\n\n")
		return
	}

	fmt.Printf("%-10s %-12s %-12s %-12s %-12s %s\n",
		"Name", "VirtAddr", "VirtSize", "RawAddr", "RawSize", "Characteristics")
	fmt.Printf("%s\n", strings.Repeat("-", 80))

	for _, section := range pe.Sections {
		if section == nil {
			continue
		}

		characteristics := ""
		if section.IsExecutable() {
			characteristics += "X"
		} else {
			characteristics += " "
		}
		if section.IsReadable() {
			characteristics += "R"
		} else {
			characteristics += " "
		}
		if section.IsWritable() {
			characteristics += "W"
		} else {
			characteristics += " "
		}

		fmt.Printf("%-10s 0x%08X   0x%08X   0x%08X   0x%08X   %s\n",
			section.GetName(),
			section.VirtualAddress,
			section.VirtualSize,
			section.PointerToRawData,
			section.SizeOfRawData,
			characteristics)
	}

	fmt.Println()
}

func showPEImports(pe *pefile.PE) {
	fmt.Printf("=== Imports ===\n")

	if len(pe.Imports) == 0 {
		fmt.Printf("No imports found\n\n")
		return
	}

	for _, imp := range pe.Imports {
		fmt.Printf("DLL: %s\n", imp.DLL)

		if len(imp.Imports) == 0 {
			fmt.Printf("  (no symbols)\n")
		} else {
			for _, symbol := range imp.Imports {
				if symbol.IsImportByOrdinal() {
					fmt.Printf("  ord%d\n", symbol.GetOrdinal())
				} else {
					fmt.Printf("  %s (hint: %d)\n", symbol.GetName(), symbol.GetHint())
				}
			}
		}
		fmt.Println()
	}
}

func showPEExports(pe *pefile.PE) {
	fmt.Printf("=== Exports ===\n")

	if pe.ExportDirectory == nil {
		fmt.Printf("No export directory found\n\n")
		return
	}

	fmt.Printf("DLL Name: %s\n", pe.ExportDirectory.DLLName)
	fmt.Printf("Base ordinal: %d\n", pe.ExportDirectory.Base)
	fmt.Printf("Number of functions: %d\n", pe.ExportDirectory.NumberOfFunctions)
	fmt.Printf("Number of names: %d\n", pe.ExportDirectory.NumberOfNames)
	fmt.Println()

	if len(pe.ExportDirectory.Exports) == 0 {
		fmt.Printf("No exports found\n\n")
		return
	}

	fmt.Printf("%-6s %-30s %-12s %s\n", "Ord", "Name", "Address", "Forwarder")
	fmt.Printf("%s\n", strings.Repeat("-", 70))

	for _, exp := range pe.ExportDirectory.Exports {
		name := exp.GetName()
		if len(name) > 30 {
			name = name[:27] + "..."
		}

		if exp.IsForwarded() {
			fmt.Printf("%-6d %-30s %-12s %s\n",
				exp.GetOrdinal(), name, "-", exp.GetForwarder())
		} else {
			fmt.Printf("%-6d %-30s 0x%08X\n",
				exp.GetOrdinal(), name, exp.GetAddress())
		}
	}

	fmt.Println()
}
