# pefile-go Examples

This directory contains example programs demonstrating how to use the pefile-go library.

## Examples

### basic_usage.go

A simple example that demonstrates the basic functionality of the library:

- Parsing PE files
- Reading headers (DOS, NT, File, Optional)
- Analyzing sections
- Listing imports and exports
- Basic file information

**Usage:**
```bash
go run basic_usage.go <pe-file>
```

**Example output:**
```
=== Basic Information ===
File: example.exe
Size: 73728 bytes
Architecture: 32-bit
File Type: Executable (EXE)

=== File Header ===
Machine: IMAGE_FILE_MACHINE_I386 (0x014C)
Number of sections: 4
Time/Date stamp: 0x12345678
Characteristics:
  - Executable image
  - Large address aware

=== Optional Header (32-bit) ===
Entry point: 0x00001000
Image base: 0x00400000
Section alignment: 0x00001000
File alignment: 0x00000200
Subsystem: IMAGE_SUBSYSTEM_WINDOWS_CUI

=== Sections ===
Section 0: .text
  Virtual Address: 0x00001000
  Virtual Size: 0x00002000 (8192 bytes)
  Raw Address: 0x00000400
  Raw Size: 0x00001E00 (7680 bytes)
  Characteristics: CODE | EXECUTE | READ

...
```

### advanced_analysis.go

A more comprehensive example that demonstrates advanced analysis features:

- File hash calculations (MD5, SHA1, SHA256)
- Section entropy analysis
- Security analysis (ASLR, DEP, suspicious sections)
- Import/export analysis with categorization
- Suspicious function detection

**Usage:**
```bash
go run advanced_analysis.go <pe-file>
```

**Example output:**
```
=== Advanced PE Analysis ===
File: example.exe

=== File Hashes ===
MD5:    d41d8cd98f00b204e9800998ecf8427e
SHA1:   da39a3ee5e6b4b0d3255bfef95601890afd80709
SHA256: e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855

=== Section Analysis ===
Total sections: 4
Total virtual size: 0x00008000 (32768 bytes)
Total raw size: 0x00007A00 (31232 bytes)
Executable sections: 1
Writable sections: 2

Section details:
  .text     : VSize=0x00002000 RSize=0x00001E00 Entropy=6.45
  .rdata    : VSize=0x00001000 RSize=0x00000800 Entropy=4.23
  .data     : VSize=0x00001000 RSize=0x00000200 Entropy=1.20
  .rsrc     : VSize=0x00004000 RSize=0x00003A00 Entropy=7.89

=== Import Analysis ===
Total DLLs: 3 (System: 3, User: 0)
Total functions: 25
Ordinal imports: 0

Potentially suspicious imports:
  kernel32.dll:
    - CreateProcessA
    - VirtualAlloc
  advapi32.dll:
    - RegSetValueExA

Top imported DLLs:
  kernel32.dll: 15 functions
  user32.dll: 8 functions
  advapi32.dll: 2 functions

=== Export Analysis ===
No exports found

=== Security Analysis ===
Potential security issues:
  - ASLR not enabled
  - DEP/NX not enabled
```

## Building and Running

To build and run the examples:

```bash
# From the repository root
cd examples

# Run basic usage example
go run basic_usage.go /path/to/pe/file.exe

# Run advanced analysis example  
go run advanced_analysis.go /path/to/pe/file.exe
```

## Common Use Cases

### 1. Malware Analysis

```go
pe, err := pefile.NewPE("suspicious.exe")
if err != nil {
    log.Fatal(err)
}

// Check for packed/encrypted sections (high entropy)
for _, section := range pe.Sections {
    if data, err := section.GetData(); err == nil {
        entropy := calculateEntropy(data)
        if entropy > 7.0 {
            fmt.Printf("High entropy section: %s (%.2f)\n", section.GetName(), entropy)
        }
    }
}

// Look for suspicious imports
for _, imp := range pe.Imports {
    for _, symbol := range imp.Imports {
        name := symbol.GetName()
        if strings.Contains(name, "Process") || strings.Contains(name, "Memory") {
            fmt.Printf("Suspicious import: %s from %s\n", name, imp.DLL)
        }
    }
}
```

### 2. Dependency Analysis

```go
pe, err := pefile.NewPE("application.exe")
if err != nil {
    log.Fatal(err)
}

fmt.Println("Dependencies:")
for _, imp := range pe.Imports {
    fmt.Printf("  %s (%d functions)\n", imp.DLL, len(imp.Imports))
}
```

### 3. Export Enumeration

```go
pe, err := pefile.NewPE("library.dll")
if err != nil {
    log.Fatal(err)
}

if pe.ExportDirectory != nil {
    fmt.Printf("Exported functions from %s:\n", pe.ExportDirectory.DLLName)
    for _, exp := range pe.ExportDirectory.Exports {
        if exp.HasName() {
            fmt.Printf("  %s (ordinal %d)\n", exp.GetName(), exp.GetOrdinal())
        }
    }
}
```

### 4. Section Analysis

```go
pe, err := pefile.NewPE("binary.exe")
if err != nil {
    log.Fatal(err)
}

for _, section := range pe.Sections {
    fmt.Printf("Section: %s\n", section.GetName())
    fmt.Printf("  Virtual: 0x%08X - 0x%08X\n", 
        section.VirtualAddress, 
        section.VirtualAddress + section.VirtualSize)
    fmt.Printf("  Permissions: %s%s%s\n",
        map[bool]string{true: "R", false: "-"}[section.IsReadable()],
        map[bool]string{true: "W", false: "-"}[section.IsWritable()],
        map[bool]string{true: "X", false: "-"}[section.IsExecutable()])
}
```

## Error Handling

The library provides comprehensive error handling and warnings:

```go
pe, err := pefile.NewPE("file.exe")
if err != nil {
    log.Fatalf("Failed to parse PE: %v", err)
}

// Check for parsing warnings
warnings := pe.GetWarnings()
if len(warnings) > 0 {
    fmt.Println("Parsing warnings:")
    for _, warning := range warnings {
        fmt.Printf("  %s\n", warning)
    }
}
```

## Performance Considerations

For large files or when you only need basic information, use fast loading:

```go
pe, err := pefile.NewPEWithOptions("large_file.exe", true) // fastLoad = true
if err != nil {
    log.Fatal(err)
}

// This will skip parsing data directories (imports, exports, etc.)
// but still parse headers and sections
```
