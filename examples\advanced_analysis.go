package main

import (
	"crypto/md5"
	"crypto/sha1"
	"crypto/sha256"
	"fmt"
	"log"
	"os"
	"sort"
	"strings"

	"github.com/pefile-go/pefile/pefile"
)

func main() {
	if len(os.Args) != 2 {
		fmt.Printf("Usage: %s <pe-file>\n", os.Args[0])
		os.Exit(1)
	}

	filename := os.Args[1]

	// Parse the PE file
	pe, err := pefile.NewPE(filename)
	if err != nil {
		log.Fatalf("Error parsing PE file: %v", err)
	}
	defer pe.Close()

	fmt.Printf("=== Advanced PE Analysis ===\n")
	fmt.Printf("File: %s\n", filename)
	fmt.Println()

	// Calculate file hashes
	calculateFileHashes(pe)

	// Analyze sections
	analyzeSections(pe)

	// Analyze imports
	analyzeImports(pe)

	// Analyze exports
	analyzeExports(pe)

	// Security analysis
	securityAnalysis(pe)
}

func calculateFileHashes(pe *pefile.PE) {
	fmt.Printf("=== File Hashes ===\n")
	
	data := pe.Data()
	
	// MD5
	md5Hash := md5.Sum(data)
	fmt.Printf("MD5:    %x\n", md5Hash)
	
	// SHA1
	sha1Hash := sha1.Sum(data)
	fmt.Printf("SHA1:   %x\n", sha1Hash)
	
	// SHA256
	sha256Hash := sha256.Sum256(data)
	fmt.Printf("SHA256: %x\n", sha256Hash)
	
	fmt.Println()
}

func analyzeSections(pe *pefile.PE) {
	fmt.Printf("=== Section Analysis ===\n")
	
	if len(pe.Sections) == 0 {
		fmt.Printf("No sections found\n\n")
		return
	}

	fmt.Printf("Total sections: %d\n", len(pe.Sections))
	
	var totalVirtualSize, totalRawSize uint32
	executableSections := 0
	writableSections := 0
	
	for _, section := range pe.Sections {
		if section == nil {
			continue
		}
		
		totalVirtualSize += section.VirtualSize
		totalRawSize += section.SizeOfRawData
		
		if section.IsExecutable() {
			executableSections++
		}
		if section.IsWritable() {
			writableSections++
		}
	}
	
	fmt.Printf("Total virtual size: 0x%08X (%d bytes)\n", totalVirtualSize, totalVirtualSize)
	fmt.Printf("Total raw size: 0x%08X (%d bytes)\n", totalRawSize, totalRawSize)
	fmt.Printf("Executable sections: %d\n", executableSections)
	fmt.Printf("Writable sections: %d\n", writableSections)
	
	// Find suspicious sections
	fmt.Printf("\nSection details:\n")
	for _, section := range pe.Sections {
		if section == nil {
			continue
		}
		
		name := section.GetName()
		if name == "" {
			name = "(unnamed)"
		}
		
		// Calculate entropy if we can get section data
		entropyStr := "N/A"
		if data, err := section.GetData(); err == nil && len(data) > 0 {
			entropy := calculateEntropy(data)
			entropyStr = fmt.Sprintf("%.2f", entropy)
		}
		
		// Check for suspicious characteristics
		suspicious := ""
		if section.IsExecutable() && section.IsWritable() {
			suspicious += " [RWX-SUSPICIOUS]"
		}
		if section.VirtualSize > 0 && section.SizeOfRawData == 0 {
			suspicious += " [VIRTUAL-ONLY]"
		}
		if section.SizeOfRawData > section.VirtualSize*2 {
			suspicious += " [LARGE-RAW]"
		}
		
		fmt.Printf("  %-10s: VSize=0x%08X RSize=0x%08X Entropy=%s%s\n", 
			name, section.VirtualSize, section.SizeOfRawData, entropyStr, suspicious)
	}
	
	fmt.Println()
}

func analyzeImports(pe *pefile.PE) {
	fmt.Printf("=== Import Analysis ===\n")
	
	if len(pe.Imports) == 0 {
		fmt.Printf("No imports found\n\n")
		return
	}

	// Count imports by category
	systemDLLs := 0
	userDLLs := 0
	totalFunctions := 0
	ordinalImports := 0
	
	suspiciousFunctions := []string{
		"CreateProcess", "WriteProcessMemory", "VirtualAlloc", "VirtualProtect",
		"LoadLibrary", "GetProcAddress", "CreateFile", "WriteFile",
		"RegSetValue", "RegCreateKey", "CreateService", "StartService",
	}
	
	foundSuspicious := make(map[string][]string)
	
	for _, imp := range pe.Imports {
		dll := strings.ToLower(imp.DLL)
		
		// Categorize DLLs
		if isSystemDLL(dll) {
			systemDLLs++
		} else {
			userDLLs++
		}
		
		totalFunctions += len(imp.Imports)
		
		// Check for suspicious functions
		for _, symbol := range imp.Imports {
			if symbol.IsImportByOrdinal() {
				ordinalImports++
			} else {
				funcName := symbol.GetName()
				for _, suspicious := range suspiciousFunctions {
					if strings.Contains(strings.ToLower(funcName), strings.ToLower(suspicious)) {
						foundSuspicious[imp.DLL] = append(foundSuspicious[imp.DLL], funcName)
					}
				}
			}
		}
	}
	
	fmt.Printf("Total DLLs: %d (System: %d, User: %d)\n", len(pe.Imports), systemDLLs, userDLLs)
	fmt.Printf("Total functions: %d\n", totalFunctions)
	fmt.Printf("Ordinal imports: %d\n", ordinalImports)
	
	if len(foundSuspicious) > 0 {
		fmt.Printf("\nPotentially suspicious imports:\n")
		for dll, functions := range foundSuspicious {
			fmt.Printf("  %s:\n", dll)
			for _, function := range functions {
				fmt.Printf("    - %s\n", function)
			}
		}
	}
	
	// Show most imported DLLs
	fmt.Printf("\nTop imported DLLs:\n")
	type dllCount struct {
		name  string
		count int
	}
	
	var dllCounts []dllCount
	for _, imp := range pe.Imports {
		dllCounts = append(dllCounts, dllCount{imp.DLL, len(imp.Imports)})
	}
	
	sort.Slice(dllCounts, func(i, j int) bool {
		return dllCounts[i].count > dllCounts[j].count
	})
	
	for i, dc := range dllCounts {
		if i >= 5 { // Show top 5
			break
		}
		fmt.Printf("  %s: %d functions\n", dc.name, dc.count)
	}
	
	fmt.Println()
}

func analyzeExports(pe *pefile.PE) {
	fmt.Printf("=== Export Analysis ===\n")
	
	if pe.ExportDirectory == nil {
		fmt.Printf("No exports found\n\n")
		return
	}

	exports := pe.ExportDirectory.Exports
	fmt.Printf("Total exports: %d\n", len(exports))
	
	namedExports := 0
	forwardedExports := 0
	
	for _, exp := range exports {
		if exp.HasName() {
			namedExports++
		}
		if exp.IsForwarded() {
			forwardedExports++
		}
	}
	
	fmt.Printf("Named exports: %d\n", namedExports)
	fmt.Printf("Ordinal-only exports: %d\n", len(exports)-namedExports)
	fmt.Printf("Forwarded exports: %d\n", forwardedExports)
	
	if forwardedExports > 0 {
		fmt.Printf("\nForwarded exports:\n")
		for _, exp := range exports {
			if exp.IsForwarded() {
				fmt.Printf("  %s -> %s\n", exp.GetName(), exp.GetForwarder())
			}
		}
	}
	
	fmt.Println()
}

func securityAnalysis(pe *pefile.PE) {
	fmt.Printf("=== Security Analysis ===\n")
	
	var issues []string
	
	// Check for ASLR
	if pe.OptionalHeader32 != nil {
		if pe.OptionalHeader32.DllCharacteristics&0x0040 == 0 { // IMAGE_DLLCHARACTERISTICS_DYNAMIC_BASE
			issues = append(issues, "ASLR not enabled")
		}
	} else if pe.OptionalHeader64 != nil {
		if pe.OptionalHeader64.DllCharacteristics&0x0040 == 0 {
			issues = append(issues, "ASLR not enabled")
		}
	}
	
	// Check for DEP/NX
	if pe.OptionalHeader32 != nil {
		if pe.OptionalHeader32.DllCharacteristics&0x0100 == 0 { // IMAGE_DLLCHARACTERISTICS_NX_COMPAT
			issues = append(issues, "DEP/NX not enabled")
		}
	} else if pe.OptionalHeader64 != nil {
		if pe.OptionalHeader64.DllCharacteristics&0x0100 == 0 {
			issues = append(issues, "DEP/NX not enabled")
		}
	}
	
	// Check for executable and writable sections
	for _, section := range pe.Sections {
		if section != nil && section.IsExecutable() && section.IsWritable() {
			issues = append(issues, fmt.Sprintf("Section %s is both executable and writable", section.GetName()))
		}
	}
	
	if len(issues) == 0 {
		fmt.Printf("No obvious security issues found\n")
	} else {
		fmt.Printf("Potential security issues:\n")
		for _, issue := range issues {
			fmt.Printf("  - %s\n", issue)
		}
	}
	
	fmt.Println()
}

func isSystemDLL(dll string) bool {
	systemDLLs := []string{
		"kernel32.dll", "ntdll.dll", "user32.dll", "gdi32.dll", "advapi32.dll",
		"shell32.dll", "ole32.dll", "oleaut32.dll", "msvcrt.dll", "ws2_32.dll",
		"wininet.dll", "urlmon.dll", "shlwapi.dll", "comctl32.dll", "comdlg32.dll",
	}
	
	for _, sysDLL := range systemDLLs {
		if dll == sysDLL {
			return true
		}
	}
	return false
}

func calculateEntropy(data []byte) float64 {
	if len(data) == 0 {
		return 0
	}
	
	// Count byte frequencies
	freq := make([]int, 256)
	for _, b := range data {
		freq[b]++
	}
	
	// Calculate entropy
	entropy := 0.0
	length := float64(len(data))
	
	for _, count := range freq {
		if count > 0 {
			p := float64(count) / length
			entropy -= p * (float64(count) / length)
		}
	}
	
	return entropy * 8.0 // Convert to bits
}
