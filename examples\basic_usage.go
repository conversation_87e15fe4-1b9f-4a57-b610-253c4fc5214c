package main

import (
	"fmt"
	"log"
	"os"

	"github.com/pefile-go/pefile/pefile"
)

func main() {
	if len(os.Args) != 2 {
		fmt.Printf("Usage: %s <pe-file>\n", os.Args[0])
		os.Exit(1)
	}

	filename := os.Args[1]

	// Parse the PE file
	pe, err := pefile.NewPE(filename)
	if err != nil {
		log.Fatalf("Error parsing PE file: %v", err)
	}
	defer pe.Close()

	// Basic file information
	fmt.Printf("=== Basic Information ===\n")
	fmt.Printf("File: %s\n", pe.Filename())
	fmt.Printf("Size: %d bytes\n", pe.Size())
	fmt.Printf("Architecture: %s\n", getArchitecture(pe))
	fmt.Printf("File Type: %s\n", getFileType(pe))
	fmt.Println()

	// Machine type and characteristics
	if pe.FileHeader != nil {
		fmt.Printf("=== File Header ===\n")
		fmt.Printf("Machine: %s (0x%04X)\n", 
			pefile.MachineTypeNames[pe.FileHeader.Machine], 
			pe.FileHeader.Machine)
		fmt.Printf("Number of sections: %d\n", pe.FileHeader.NumberOfSections)
		fmt.Printf("Time/Date stamp: 0x%08X\n", pe.FileHeader.TimeDateStamp)
		
		// Show some key characteristics
		fmt.Printf("Characteristics:\n")
		if pe.FileHeader.Characteristics&pefile.ImageFileExecutableImage != 0 {
			fmt.Printf("  - Executable image\n")
		}
		if pe.FileHeader.Characteristics&pefile.ImageFileDLL != 0 {
			fmt.Printf("  - Dynamic Link Library\n")
		}
		if pe.FileHeader.Characteristics&pefile.ImageFileLargeAddressAware != 0 {
			fmt.Printf("  - Large address aware\n")
		}
		fmt.Println()
	}

	// Optional header information
	if pe.OptionalHeader32 != nil {
		fmt.Printf("=== Optional Header (32-bit) ===\n")
		fmt.Printf("Entry point: 0x%08X\n", pe.OptionalHeader32.AddressOfEntryPoint)
		fmt.Printf("Image base: 0x%08X\n", pe.OptionalHeader32.ImageBase)
		fmt.Printf("Section alignment: 0x%08X\n", pe.OptionalHeader32.SectionAlignment)
		fmt.Printf("File alignment: 0x%08X\n", pe.OptionalHeader32.FileAlignment)
		fmt.Printf("Subsystem: %s\n", pefile.SubsystemTypeNames[pe.OptionalHeader32.Subsystem])
		fmt.Println()
	} else if pe.OptionalHeader64 != nil {
		fmt.Printf("=== Optional Header (64-bit) ===\n")
		fmt.Printf("Entry point: 0x%08X\n", pe.OptionalHeader64.AddressOfEntryPoint)
		fmt.Printf("Image base: 0x%016X\n", pe.OptionalHeader64.ImageBase)
		fmt.Printf("Section alignment: 0x%08X\n", pe.OptionalHeader64.SectionAlignment)
		fmt.Printf("File alignment: 0x%08X\n", pe.OptionalHeader64.FileAlignment)
		fmt.Printf("Subsystem: %s\n", pefile.SubsystemTypeNames[pe.OptionalHeader64.Subsystem])
		fmt.Println()
	}

	// Section information
	fmt.Printf("=== Sections ===\n")
	if len(pe.Sections) == 0 {
		fmt.Printf("No sections found\n")
	} else {
		for i, section := range pe.Sections {
			if section == nil {
				continue
			}
			
			fmt.Printf("Section %d: %s\n", i, section.GetName())
			fmt.Printf("  Virtual Address: 0x%08X\n", section.VirtualAddress)
			fmt.Printf("  Virtual Size: 0x%08X (%d bytes)\n", section.VirtualSize, section.VirtualSize)
			fmt.Printf("  Raw Address: 0x%08X\n", section.PointerToRawData)
			fmt.Printf("  Raw Size: 0x%08X (%d bytes)\n", section.SizeOfRawData, section.SizeOfRawData)
			
			// Section characteristics
			fmt.Printf("  Characteristics: ")
			var chars []string
			if section.ContainsCode() {
				chars = append(chars, "CODE")
			}
			if section.ContainsData() {
				chars = append(chars, "DATA")
			}
			if section.IsExecutable() {
				chars = append(chars, "EXECUTE")
			}
			if section.IsReadable() {
				chars = append(chars, "READ")
			}
			if section.IsWritable() {
				chars = append(chars, "WRITE")
			}
			if section.IsDiscardable() {
				chars = append(chars, "DISCARDABLE")
			}
			
			if len(chars) > 0 {
				for i, char := range chars {
					if i > 0 {
						fmt.Printf(" | ")
					}
					fmt.Printf("%s", char)
				}
			} else {
				fmt.Printf("(none)")
			}
			fmt.Println()
			fmt.Println()
		}
	}

	// Import information
	fmt.Printf("=== Imports ===\n")
	if len(pe.Imports) == 0 {
		fmt.Printf("No imports found\n")
	} else {
		fmt.Printf("Number of imported DLLs: %d\n", len(pe.Imports))
		
		totalSymbols := 0
		for _, imp := range pe.Imports {
			totalSymbols += len(imp.Imports)
		}
		fmt.Printf("Total imported symbols: %d\n", totalSymbols)
		fmt.Println()
		
		// Show first few imports as examples
		for i, imp := range pe.Imports {
			if i >= 3 { // Limit to first 3 DLLs
				fmt.Printf("... and %d more DLLs\n", len(pe.Imports)-i)
				break
			}
			
			fmt.Printf("DLL: %s (%d symbols)\n", imp.DLL, len(imp.Imports))
			
			// Show first few symbols
			for j, symbol := range imp.Imports {
				if j >= 5 { // Limit to first 5 symbols per DLL
					fmt.Printf("    ... and %d more symbols\n", len(imp.Imports)-j)
					break
				}
				
				if symbol.IsImportByOrdinal() {
					fmt.Printf("    ord%d\n", symbol.GetOrdinal())
				} else {
					fmt.Printf("    %s\n", symbol.GetName())
				}
			}
			fmt.Println()
		}
	}

	// Export information
	fmt.Printf("=== Exports ===\n")
	if pe.ExportDirectory == nil {
		fmt.Printf("No export directory found\n")
	} else {
		fmt.Printf("DLL Name: %s\n", pe.ExportDirectory.DLLName)
		fmt.Printf("Number of exported functions: %d\n", len(pe.ExportDirectory.Exports))
		fmt.Printf("Base ordinal: %d\n", pe.ExportDirectory.Base)
		fmt.Println()
		
		if len(pe.ExportDirectory.Exports) > 0 {
			// Show first few exports as examples
			fmt.Printf("Sample exports:\n")
			for i, exp := range pe.ExportDirectory.Exports {
				if i >= 10 { // Limit to first 10 exports
					fmt.Printf("... and %d more exports\n", len(pe.ExportDirectory.Exports)-i)
					break
				}
				
				if exp.IsForwarded() {
					fmt.Printf("  %s (ord %d) -> %s\n", exp.GetName(), exp.GetOrdinal(), exp.GetForwarder())
				} else {
					fmt.Printf("  %s (ord %d) @ 0x%08X\n", exp.GetName(), exp.GetOrdinal(), exp.GetAddress())
				}
			}
		}
	}
	fmt.Println()

	// Show any warnings
	warnings := pe.GetWarnings()
	if len(warnings) > 0 {
		fmt.Printf("=== Warnings ===\n")
		for _, warning := range warnings {
			fmt.Printf("  %s\n", warning)
		}
		fmt.Println()
	}
}

func getArchitecture(pe *pefile.PE) string {
	if pe.Is64Bit() {
		return "64-bit"
	} else if pe.Is32Bit() {
		return "32-bit"
	}
	return "Unknown"
}

func getFileType(pe *pefile.PE) string {
	if pe.IsDLL() {
		return "Dynamic Link Library (DLL)"
	} else if pe.IsExecutable() {
		return "Executable (EXE)"
	}
	return "Unknown"
}
