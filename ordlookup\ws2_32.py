ord_names = {
    1: b"accept",
    2: b"bind",
    3: b"closesocket",
    4: b"connect",
    5: b"getpeername",
    6: b"getsockname",
    7: b"getsockopt",
    8: b"htonl",
    9: b"htons",
    10: b"ioctlsocket",
    11: b"inet_addr",
    12: b"inet_ntoa",
    13: b"listen",
    14: b"ntohl",
    15: b"ntohs",
    16: b"recv",
    17: b"recvfrom",
    18: b"select",
    19: b"send",
    20: b"sendto",
    21: b"setsockopt",
    22: b"shutdown",
    23: b"socket",
    24: b"WSApSetPostRoutine",
    25: b"FreeAddrInfoEx",
    26: b"FreeAddrInfoExW",
    27: b"FreeAddrInfoW",
    28: b"GetAddrInfoExA",
    29: b"GetAddrInfoExCancel",
    30: b"GetAddrInfoExOverlappedResult",
    31: b"GetAddrInfoExW",
    32: b"GetAddrInfoW",
    33: b"GetHostNameW",
    34: b"GetNameInfoW",
    35: b"InetNtopW",
    36: b"InetPtonW",
    37: b"ProcessSocketNotifications",
    38: b"SetAddrInfoExA",
    39: b"SetAddrInfoExW",
    40: b"WPUCompleteOverlappedRequest",
    41: b"WPUGetProviderPathEx",
    42: b"WSAAccept",
    43: b"WSAAddressToStringA",
    44: b"WSAAddressToStringW",
    45: b"WSAAdvertiseProvider",
    46: b"WSACloseEvent",
    47: b"WSAConnect",
    48: b"WSAConnectByList",
    49: b"WSAConnectByNameA",
    50: b"WSAConnectByNameW",
    51: b"gethostbyaddr",
    52: b"gethostbyname",
    53: b"getprotobyname",
    54: b"getprotobynumber",
    55: b"getservbyname",
    56: b"getservbyport",
    57: b"gethostname",
    58: b"WSACreateEvent",
    59: b"WSADuplicateSocketA",
    60: b"WSADuplicateSocketW",
    61: b"WSAEnumNameSpaceProvidersA",
    62: b"WSAEnumNameSpaceProvidersExA",
    63: b"WSAEnumNameSpaceProvidersExW",
    64: b"WSAEnumNameSpaceProvidersW",
    65: b"WSAEnumNetworkEvents",
    66: b"WSAEnumProtocolsA",
    67: b"WSAEnumProtocolsW",
    68: b"WSAEventSelect",
    69: b"WSAGetOverlappedResult",
    70: b"WSAGetQOSByName",
    71: b"WSAGetServiceClassInfoA",
    72: b"WSAGetServiceClassInfoW",
    73: b"WSAGetServiceClassNameByClassIdA",
    74: b"WSAGetServiceClassNameByClassIdW",
    75: b"WSAHtonl",
    76: b"WSAHtons",
    77: b"WSAInstallServiceClassA",
    78: b"WSAInstallServiceClassW",
    79: b"WSAIoctl",
    80: b"WSAJoinLeaf",
    81: b"WSALookupServiceBeginA",
    82: b"WSALookupServiceBeginW",
    83: b"WSALookupServiceEnd",
    84: b"WSALookupServiceNextA",
    85: b"WSALookupServiceNextW",
    86: b"WSANSPIoctl",
    87: b"WSANtohl",
    88: b"WSANtohs",
    89: b"WSAPoll",
    90: b"WSAProviderCompleteAsyncCall",
    91: b"WSAProviderConfigChange",
    92: b"WSARecv",
    93: b"WSARecvDisconnect",
    94: b"WSARecvFrom",
    95: b"WSARemoveServiceClass",
    96: b"WSAResetEvent",
    97: b"WSASend",
    98: b"WSASendDisconnect",
    99: b"WSASendMsg",
    100: b"WSASendTo",
    101: b"WSAAsyncSelect",
    102: b"WSAAsyncGetHostByAddr",
    103: b"WSAAsyncGetHostByName",
    104: b"WSAAsyncGetProtoByNumber",
    105: b"WSAAsyncGetProtoByName",
    106: b"WSAAsyncGetServByPort",
    107: b"WSAAsyncGetServByName",
    108: b"WSACancelAsyncRequest",
    109: b"WSASetBlockingHook",
    110: b"WSAUnhookBlockingHook",
    111: b"WSAGetLastError",
    112: b"WSASetLastError",
    113: b"WSACancelBlockingCall",
    114: b"WSAIsBlocking",
    115: b"WSAStartup",
    116: b"WSACleanup",
    117: b"WSASetEvent",
    118: b"WSASetServiceA",
    119: b"WSASetServiceW",
    120: b"WSASocketA",
    121: b"WSASocketW",
    122: b"WSAStringToAddressA",
    123: b"WSAStringToAddressW",
    124: b"WSAUnadvertiseProvider",
    125: b"WSAWaitForMultipleEvents",
    126: b"WSCDeinstallProvider",
    127: b"WSCDeinstallProvider32",
    128: b"WSCDeinstallProviderEx",
    129: b"WSCEnableNSProvider",
    130: b"WSCEnableNSProvider32",
    131: b"WSCEnumNameSpaceProviders32",
    132: b"WSCEnumNameSpaceProvidersEx32",
    133: b"WSCEnumProtocols",
    134: b"WSCEnumProtocols32",
    135: b"WSCEnumProtocolsEx",
    136: b"WSCGetApplicationCategory",
    137: b"WSCGetApplicationCategoryEx",
    138: b"WSCGetProviderInfo",
    139: b"WSCGetProviderInfo32",
    140: b"WSCGetProviderPath",
    141: b"WSCGetProviderPath32",
    142: b"WSCInstallNameSpace",
    143: b"WSCInstallNameSpace32",
    144: b"WSCInstallNameSpaceEx",
    145: b"WSCInstallNameSpaceEx2",
    146: b"WSCInstallNameSpaceEx32",
    147: b"WSCInstallProvider",
    148: b"WSCInstallProvider64_32",
    149: b"WSCInstallProviderAndChains64_32",
    150: b"WSCInstallProviderEx",
    151: b"__WSAFDIsSet",
    152: b"WSCSetApplicationCategory",
    153: b"WSCSetApplicationCategoryEx",
    154: b"WSCSetProviderInfo",
    155: b"WSCSetProviderInfo32",
    156: b"WSCUnInstallNameSpace",
    157: b"WSCUnInstallNameSpace32",
    158: b"WSCUnInstallNameSpaceEx2",
    159: b"WSCUpdateProvider",
    160: b"WSCUpdateProvider32",
    161: b"WSCUpdateProviderEx",
    162: b"WSCWriteNameSpaceOrder",
    163: b"WSCWriteNameSpaceOrder32",
    164: b"WSCWriteProviderOrder",
    165: b"WSCWriteProviderOrder32",
    166: b"WSCWriteProviderOrderEx",
    167: b"WahCloseApcHelper",
    168: b"WahCloseHandleHelper",
    169: b"WahCloseNotificationHandleHelper",
    170: b"WahCloseSocketHandle",
    171: b"WahCloseThread",
    172: b"WahCompleteRequest",
    173: b"WahCreateHandleContextTable",
    174: b"WahCreateNotificationHandle",
    175: b"WahCreateSocketHandle",
    176: b"WahDestroyHandleContextTable",
    177: b"WahDisableNonIFSHandleSupport",
    178: b"WahEnableNonIFSHandleSupport",
    179: b"WahEnumerateHandleContexts",
    180: b"WahInsertHandleContext",
    181: b"WahNotifyAllProcesses",
    182: b"WahOpenApcHelper",
    183: b"WahOpenCurrentThread",
    184: b"WahOpenHandleHelper",
    185: b"WahOpenNotificationHandleHelper",
    186: b"WahQueueUserApc",
    187: b"WahReferenceContextByHandle",
    188: b"WahRemoveHandleContext",
    189: b"WahWaitForNotification",
    190: b"WahWriteLSPEvent",
    191: b"freeaddrinfo",
    192: b"getaddrinfo",
    193: b"getnameinfo",
    194: b"inet_ntop",
    195: b"inet_pton",
    500: b"WEP",
}
