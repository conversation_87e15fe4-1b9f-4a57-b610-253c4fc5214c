package pefile

import (
	"testing"
)

// BenchmarkPEParsing benchmarks PE file parsing
func BenchmarkPEParsing(b *testing.B) {
	// Create a realistic PE file data for benchmarking
	data := createRealisticPEData()

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		pe, err := NewPEFromData(data, "benchmark.exe", false)
		if err != nil {
			b.Fatalf("Failed to parse PE: %v", err)
		}
		pe.Close()
	}
}

// BenchmarkPEParsingFast benchmarks fast PE file parsing
func BenchmarkPEParsingFast(b *testing.B) {
	// Create a realistic PE file data for benchmarking
	data := createRealisticPEData()

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		pe, err := NewPEFromData(data, "benchmark.exe", true) // Fast loading
		if err != nil {
			b.Fatalf("Failed to parse PE: %v", err)
		}
		pe.Close()
	}
}

// BenchmarkSectionParsing benchmarks section parsing
func BenchmarkSectionParsing(b *testing.B) {
	data := createRealisticPEData()
	pe, err := NewPEFromData(data, "benchmark.exe", false)
	if err != nil {
		b.Fatalf("Failed to parse PE: %v", err)
	}
	defer pe.Close()

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		for _, section := range pe.Sections {
			if section != nil {
				_, _ = section.GetData()
			}
		}
	}
}

// BenchmarkImportParsing benchmarks import parsing
func BenchmarkImportParsing(b *testing.B) {
	data := createRealisticPEDataWithImports()

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		pe, err := NewPEFromData(data, "benchmark.exe", false)
		if err != nil {
			b.Fatalf("Failed to parse PE: %v", err)
		}

		// Access imports to trigger parsing
		for _, imp := range pe.Imports {
			for _, symbol := range imp.Imports {
				_ = symbol.GetName()
			}
		}

		pe.Close()
	}
}

// BenchmarkHashCalculation benchmarks hash calculation
func BenchmarkHashCalculation(b *testing.B) {
	data := createRealisticPEData()
	pe, err := NewPEFromData(data, "benchmark.exe", false)
	if err != nil {
		b.Fatalf("Failed to parse PE: %v", err)
	}
	defer pe.Close()

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		_ = pe.CalculateFileHash(HashMD5)
		_ = pe.CalculateFileHash(HashSHA1)
		_ = pe.CalculateFileHash(HashSHA256)
	}
}

// BenchmarkImportHash benchmarks import hash calculation
func BenchmarkImportHash(b *testing.B) {
	data := createRealisticPEDataWithImports()
	pe, err := NewPEFromData(data, "benchmark.exe", false)
	if err != nil {
		b.Fatalf("Failed to parse PE: %v", err)
	}
	defer pe.Close()

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		_ = pe.CalculateImportHash()
	}
}

// BenchmarkEntropyCalculation benchmarks entropy calculation
func BenchmarkEntropyCalculation(b *testing.B) {
	data := make([]byte, 4096) // 4KB of data
	for i := range data {
		data[i] = byte(i % 256)
	}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		_ = CalculateEntropy(data)
	}
}

// BenchmarkRVAToOffset benchmarks RVA to offset conversion
func BenchmarkRVAToOffset(b *testing.B) {
	data := createRealisticPEData()
	pe, err := NewPEFromData(data, "benchmark.exe", false)
	if err != nil {
		b.Fatalf("Failed to parse PE: %v", err)
	}
	defer pe.Close()

	rvas := []uint32{0x1000, 0x2000, 0x3000, 0x4000, 0x5000}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		for _, rva := range rvas {
			_, _ = pe.GetOffsetFromRVA(rva)
		}
	}
}

// BenchmarkValidation benchmarks PE validation
func BenchmarkValidation(b *testing.B) {
	data := createRealisticPEData()
	pe, err := NewPEFromData(data, "benchmark.exe", false)
	if err != nil {
		b.Fatalf("Failed to parse PE: %v", err)
	}
	defer pe.Close()

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		_ = pe.ValidatePE()
	}
}

// BenchmarkOrdinalLookup benchmarks ordinal lookup
func BenchmarkOrdinalLookup(b *testing.B) {
	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		_ = OrdLookup("oleaut32.dll", 2, true)
		_ = OrdLookup("ws2_32.dll", 1, true)
		_ = OrdLookup("wsock32.dll", 1, true)
		_ = OrdLookup("unknown.dll", 123, true)
	}
}

// createRealisticPEData creates a realistic PE file data for benchmarking
func createRealisticPEData() []byte {
	data := make([]byte, 8192) // 8KB file

	// Set DOS signature
	data[0] = 0x4D // 'M'
	data[1] = 0x5A // 'Z'

	// Set e_lfanew to point to offset 0x80
	data[60] = 0x80
	data[61] = 0x00
	data[62] = 0x00
	data[63] = 0x00

	// Set NT signature at offset 0x80
	data[0x80] = 0x50 // 'P'
	data[0x81] = 0x45 // 'E'
	data[0x82] = 0x00
	data[0x83] = 0x00

	// Set machine type (i386)
	data[0x84] = 0x4C
	data[0x85] = 0x01

	// Set number of sections
	data[0x86] = 0x03
	data[0x87] = 0x00

	// Set size of optional header
	data[0x94] = 0xE0
	data[0x95] = 0x00

	// Set characteristics
	data[0x96] = 0x02
	data[0x97] = 0x01

	// Set optional header magic (PE32)
	data[0x98] = 0x0B
	data[0x99] = 0x01

	// Set entry point
	data[0x9C] = 0x00
	data[0x9D] = 0x10
	data[0x9E] = 0x00
	data[0x9F] = 0x00

	// Set image base
	data[0xA8] = 0x00
	data[0xA9] = 0x00
	data[0xAA] = 0x40
	data[0xAB] = 0x00

	// Set section alignment
	data[0xAC] = 0x00
	data[0xAD] = 0x10
	data[0xAE] = 0x00
	data[0xAF] = 0x00

	// Set file alignment
	data[0xB0] = 0x00
	data[0xB1] = 0x02
	data[0xB2] = 0x00
	data[0xB3] = 0x00

	// Set size of image
	data[0xC0] = 0x00
	data[0xC1] = 0x30
	data[0xC2] = 0x00
	data[0xC3] = 0x00

	// Set size of headers
	data[0xC4] = 0x00
	data[0xC5] = 0x04
	data[0xC6] = 0x00
	data[0xC7] = 0x00

	// Add some section headers at offset 0x178 (after optional header)
	sectionOffset := 0x178

	// .text section
	copy(data[sectionOffset:], []byte(".text\x00\x00\x00"))
	data[sectionOffset+8] = 0x00 // VirtualSize
	data[sectionOffset+9] = 0x10
	data[sectionOffset+10] = 0x00
	data[sectionOffset+11] = 0x00
	data[sectionOffset+12] = 0x00 // VirtualAddress
	data[sectionOffset+13] = 0x10
	data[sectionOffset+14] = 0x00
	data[sectionOffset+15] = 0x00
	data[sectionOffset+16] = 0x00 // SizeOfRawData
	data[sectionOffset+17] = 0x10
	data[sectionOffset+18] = 0x00
	data[sectionOffset+19] = 0x00
	data[sectionOffset+20] = 0x00 // PointerToRawData
	data[sectionOffset+21] = 0x04
	data[sectionOffset+22] = 0x00
	data[sectionOffset+23] = 0x00
	data[sectionOffset+36] = 0x20 // Characteristics (executable)
	data[sectionOffset+37] = 0x00
	data[sectionOffset+38] = 0x00
	data[sectionOffset+39] = 0x60

	return data
}

// createRealisticPEDataWithImports creates PE data with import table
func createRealisticPEDataWithImports() []byte {
	data := createRealisticPEData()

	// Add import directory to data directories
	// Import table RVA at offset 0x108 in optional header
	data[0x108] = 0x00
	data[0x109] = 0x20
	data[0x10A] = 0x00
	data[0x10B] = 0x00

	// Import table size
	data[0x10C] = 0x50
	data[0x10D] = 0x00
	data[0x10E] = 0x00
	data[0x10F] = 0x00

	// Add a simple import descriptor at RVA 0x2000 (file offset would need mapping)
	// This is simplified - in a real benchmark we'd need proper section mapping

	return data
}

// BenchmarkOptimizedRVAToOffset benchmarks optimized RVA to offset conversion
func BenchmarkOptimizedRVAToOffset(b *testing.B) {
	data := createRealisticPEData()
	pe, err := NewPEFromData(data, "benchmark.exe", false)
	if err != nil {
		b.Fatalf("Failed to parse PE: %v", err)
	}
	defer pe.Close()

	// Enable performance optimizations
	pe.EnablePerformanceOptimizations()

	rvas := []uint32{0x1000, 0x2000, 0x3000, 0x4000, 0x5000}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		for _, rva := range rvas {
			_, _ = pe.OptimizedGetOffsetFromRVA(rva)
		}
	}
}

// BenchmarkOptimizedHashCalculation benchmarks optimized hash calculation
func BenchmarkOptimizedHashCalculation(b *testing.B) {
	data := createRealisticPEData()
	pe, err := NewPEFromData(data, "benchmark.exe", false)
	if err != nil {
		b.Fatalf("Failed to parse PE: %v", err)
	}
	defer pe.Close()

	// Enable performance optimizations
	pe.EnablePerformanceOptimizations()

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		_ = pe.OptimizedCalculateFileHash(HashMD5)
		_ = pe.OptimizedCalculateFileHash(HashSHA1)
		_ = pe.OptimizedCalculateFileHash(HashSHA256)
	}
}

// BenchmarkMemoryPool benchmarks memory pool performance
func BenchmarkMemoryPool(b *testing.B) {
	pool := NewMemoryPool()

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		buf := pool.Get(1024)
		// Simulate some work
		for j := 0; j < len(buf); j++ {
			buf[j] = byte(j % 256)
		}
		pool.Put(buf)
	}
}
