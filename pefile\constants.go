// Package pefile provides comprehensive support for parsing Portable Executable (PE) files.
// This is a Go port of the popular Python pefile library.
package pefile

// PE file signatures and magic numbers
const (
	ImageDOSSignature   = 0x5A4D // "MZ"
	ImageDOSZMSignature = 0x4D5A // "ZM"
	ImageNESignature    = 0x454E // "NE"
	ImageLESignature    = 0x454C // "LE"
	ImageLXSignature    = 0x584C // "LX"
	ImageTESignature    = 0x5A56 // "VZ" - Terse Executables

	ImageNTSignature              = 0x00004550 // "PE\0\0"
	ImageNumberOfDirectoryEntries = 16
	ImageOrdinalFlag              = 0x80000000
	ImageOrdinalFlag64            = 0x8000000000000000
	OptionalHeaderMagicPE         = 0x10B // PE32
	OptionalHeaderMagicPEPlus     = 0x20B // PE32+
)

// Limits for parsing safety
const (
	MaxStringLength      = 0x100000 // 1MB
	MaxImportSymbols     = 0x2000
	MaxImportNameLength  = 0x200
	MaxDLLLength         = 0x200
	MaxSymbolNameLength  = 0x200
	MaxSections          = 0x800
	MaxResourceEntries   = 0x8000
	MaxResourceDepth     = 32
	MaxSymbolExportCount = 0x2000
)

// Directory entry types
type DirectoryEntryType uint32

const (
	DirectoryEntryExport DirectoryEntryType = iota
	DirectoryEntryImport
	DirectoryEntryResource
	DirectoryEntryException
	DirectoryEntrySecurity
	DirectoryEntryBaseReloc
	DirectoryEntryDebug
	DirectoryEntryCopyright // Architecture on non-x86 platforms
	DirectoryEntryGlobalPtr
	DirectoryEntryTLS
	DirectoryEntryLoadConfig
	DirectoryEntryBoundImport
	DirectoryEntryIAT
	DirectoryEntryDelayImport
	DirectoryEntryCOMDescriptor
	DirectoryEntryReserved
)

// DirectoryEntryNames maps directory entry types to their string names
var DirectoryEntryNames = map[DirectoryEntryType]string{
	DirectoryEntryExport:        "IMAGE_DIRECTORY_ENTRY_EXPORT",
	DirectoryEntryImport:        "IMAGE_DIRECTORY_ENTRY_IMPORT",
	DirectoryEntryResource:      "IMAGE_DIRECTORY_ENTRY_RESOURCE",
	DirectoryEntryException:     "IMAGE_DIRECTORY_ENTRY_EXCEPTION",
	DirectoryEntrySecurity:      "IMAGE_DIRECTORY_ENTRY_SECURITY",
	DirectoryEntryBaseReloc:     "IMAGE_DIRECTORY_ENTRY_BASERELOC",
	DirectoryEntryDebug:         "IMAGE_DIRECTORY_ENTRY_DEBUG",
	DirectoryEntryCopyright:     "IMAGE_DIRECTORY_ENTRY_COPYRIGHT",
	DirectoryEntryGlobalPtr:     "IMAGE_DIRECTORY_ENTRY_GLOBALPTR",
	DirectoryEntryTLS:           "IMAGE_DIRECTORY_ENTRY_TLS",
	DirectoryEntryLoadConfig:    "IMAGE_DIRECTORY_ENTRY_LOAD_CONFIG",
	DirectoryEntryBoundImport:   "IMAGE_DIRECTORY_ENTRY_BOUND_IMPORT",
	DirectoryEntryIAT:           "IMAGE_DIRECTORY_ENTRY_IAT",
	DirectoryEntryDelayImport:   "IMAGE_DIRECTORY_ENTRY_DELAY_IMPORT",
	DirectoryEntryCOMDescriptor: "IMAGE_DIRECTORY_ENTRY_COM_DESCRIPTOR",
	DirectoryEntryReserved:      "IMAGE_DIRECTORY_ENTRY_RESERVED",
}

// Image characteristics flags
type ImageCharacteristic uint16

const (
	ImageFileRelocsStripped       ImageCharacteristic = 0x0001
	ImageFileExecutableImage      ImageCharacteristic = 0x0002
	ImageFileLineNumsStripped     ImageCharacteristic = 0x0004
	ImageFileLocalSymsStripped    ImageCharacteristic = 0x0008
	ImageFileAggresiveWSTrim      ImageCharacteristic = 0x0010
	ImageFileLargeAddressAware    ImageCharacteristic = 0x0020
	ImageFile16BitMachine         ImageCharacteristic = 0x0040
	ImageFileBytesReversedLo      ImageCharacteristic = 0x0080
	ImageFile32BitMachine         ImageCharacteristic = 0x0100
	ImageFileDebugStripped        ImageCharacteristic = 0x0200
	ImageFileRemovableRunFromSwap ImageCharacteristic = 0x0400
	ImageFileNetRunFromSwap       ImageCharacteristic = 0x0800
	ImageFileSystem               ImageCharacteristic = 0x1000
	ImageFileDLL                  ImageCharacteristic = 0x2000
	ImageFileUpSystemOnly         ImageCharacteristic = 0x4000
	ImageFileBytesReversedHi      ImageCharacteristic = 0x8000
)

// ImageCharacteristicNames maps characteristics to their string names
var ImageCharacteristicNames = map[ImageCharacteristic]string{
	ImageFileRelocsStripped:       "IMAGE_FILE_RELOCS_STRIPPED",
	ImageFileExecutableImage:      "IMAGE_FILE_EXECUTABLE_IMAGE",
	ImageFileLineNumsStripped:     "IMAGE_FILE_LINE_NUMS_STRIPPED",
	ImageFileLocalSymsStripped:    "IMAGE_FILE_LOCAL_SYMS_STRIPPED",
	ImageFileAggresiveWSTrim:      "IMAGE_FILE_AGGRESIVE_WS_TRIM",
	ImageFileLargeAddressAware:    "IMAGE_FILE_LARGE_ADDRESS_AWARE",
	ImageFile16BitMachine:         "IMAGE_FILE_16BIT_MACHINE",
	ImageFileBytesReversedLo:      "IMAGE_FILE_BYTES_REVERSED_LO",
	ImageFile32BitMachine:         "IMAGE_FILE_32BIT_MACHINE",
	ImageFileDebugStripped:        "IMAGE_FILE_DEBUG_STRIPPED",
	ImageFileRemovableRunFromSwap: "IMAGE_FILE_REMOVABLE_RUN_FROM_SWAP",
	ImageFileNetRunFromSwap:       "IMAGE_FILE_NET_RUN_FROM_SWAP",
	ImageFileSystem:               "IMAGE_FILE_SYSTEM",
	ImageFileDLL:                  "IMAGE_FILE_DLL",
	ImageFileUpSystemOnly:         "IMAGE_FILE_UP_SYSTEM_ONLY",
	ImageFileBytesReversedHi:      "IMAGE_FILE_BYTES_REVERSED_HI",
}

// Machine types
type MachineType uint16

const (
	MachineUnknown   MachineType = 0x0
	MachineI386      MachineType = 0x014C
	MachineR3000     MachineType = 0x0162
	MachineR4000     MachineType = 0x0166
	MachineR10000    MachineType = 0x0168
	MachineWCEMIPSV2 MachineType = 0x0169
	MachineAlpha     MachineType = 0x0184
	MachineSH3       MachineType = 0x01A2
	MachineSH3DSP    MachineType = 0x01A3
	MachineSH3E      MachineType = 0x01A4
	MachineSH4       MachineType = 0x01A6
	MachineSH5       MachineType = 0x01A8
	MachineARM       MachineType = 0x01C0
	MachineThumb     MachineType = 0x01C2
	MachineARMNT     MachineType = 0x01C4
	MachineAM33      MachineType = 0x01D3
	MachinePowerPC   MachineType = 0x01F0
	MachinePowerPCFP MachineType = 0x01F1
	MachineIA64      MachineType = 0x0200
	MachineMIPS16    MachineType = 0x0266
	MachineAlpha64   MachineType = 0x0284
	MachineAXP64     MachineType = 0x0284 // same as Alpha64
	MachineMIPSFPU   MachineType = 0x0366
	MachineMIPSFPU16 MachineType = 0x0466
	MachineTriCore   MachineType = 0x0520
	MachineCEF       MachineType = 0x0CEF
	MachineEBC       MachineType = 0x0EBC
	MachineAMD64     MachineType = 0x8664
	MachineM32R      MachineType = 0x9041
	MachineARM64     MachineType = 0xAA64
	MachineCEE       MachineType = 0xC0EE
)

// MachineTypeNames maps machine types to their string names
var MachineTypeNames = map[MachineType]string{
	MachineUnknown:   "IMAGE_FILE_MACHINE_UNKNOWN",
	MachineI386:      "IMAGE_FILE_MACHINE_I386",
	MachineR3000:     "IMAGE_FILE_MACHINE_R3000",
	MachineR4000:     "IMAGE_FILE_MACHINE_R4000",
	MachineR10000:    "IMAGE_FILE_MACHINE_R10000",
	MachineWCEMIPSV2: "IMAGE_FILE_MACHINE_WCEMIPSV2",
	MachineAlpha:     "IMAGE_FILE_MACHINE_ALPHA",
	MachineSH3:       "IMAGE_FILE_MACHINE_SH3",
	MachineSH3DSP:    "IMAGE_FILE_MACHINE_SH3DSP",
	MachineSH3E:      "IMAGE_FILE_MACHINE_SH3E",
	MachineSH4:       "IMAGE_FILE_MACHINE_SH4",
	MachineSH5:       "IMAGE_FILE_MACHINE_SH5",
	MachineARM:       "IMAGE_FILE_MACHINE_ARM",
	MachineThumb:     "IMAGE_FILE_MACHINE_THUMB",
	MachineARMNT:     "IMAGE_FILE_MACHINE_ARMNT",
	MachineAM33:      "IMAGE_FILE_MACHINE_AM33",
	MachinePowerPC:   "IMAGE_FILE_MACHINE_POWERPC",
	MachinePowerPCFP: "IMAGE_FILE_MACHINE_POWERPCFP",
	MachineIA64:      "IMAGE_FILE_MACHINE_IA64",
	MachineMIPS16:    "IMAGE_FILE_MACHINE_MIPS16",
	MachineAlpha64:   "IMAGE_FILE_MACHINE_ALPHA64", // Note: AXP64 is same value
	MachineMIPSFPU:   "IMAGE_FILE_MACHINE_MIPSFPU",
	MachineMIPSFPU16: "IMAGE_FILE_MACHINE_MIPSFPU16",
	MachineTriCore:   "IMAGE_FILE_MACHINE_TRICORE",
	MachineCEF:       "IMAGE_FILE_MACHINE_CEF",
	MachineEBC:       "IMAGE_FILE_MACHINE_EBC",
	MachineAMD64:     "IMAGE_FILE_MACHINE_AMD64",
	MachineM32R:      "IMAGE_FILE_MACHINE_M32R",
	MachineARM64:     "IMAGE_FILE_MACHINE_ARM64",
	MachineCEE:       "IMAGE_FILE_MACHINE_CEE",
}

// SectionCharacteristicNames maps section characteristics to their string names
var SectionCharacteristicNames = map[SectionCharacteristic]string{
	SectionCntCode:              "IMAGE_SCN_CNT_CODE",
	SectionCntInitializedData:   "IMAGE_SCN_CNT_INITIALIZED_DATA",
	SectionCntUninitializedData: "IMAGE_SCN_CNT_UNINITIALIZED_DATA",
	SectionLnkOther:             "IMAGE_SCN_LNK_OTHER",
	SectionLnkInfo:              "IMAGE_SCN_LNK_INFO",
	SectionLnkRemove:            "IMAGE_SCN_LNK_REMOVE",
	SectionLnkComdat:            "IMAGE_SCN_LNK_COMDAT",
	SectionNoDeferSpecExc:       "IMAGE_SCN_NO_DEFER_SPEC_EXC",
	SectionMemPurgeable:         "IMAGE_SCN_MEM_PURGEABLE",
	SectionMemLocked:            "IMAGE_SCN_MEM_LOCKED",
	SectionMemPreload:           "IMAGE_SCN_MEM_PRELOAD",
	SectionAlign1Bytes:          "IMAGE_SCN_ALIGN_1BYTES",
	SectionAlign2Bytes:          "IMAGE_SCN_ALIGN_2BYTES",
	SectionAlign4Bytes:          "IMAGE_SCN_ALIGN_4BYTES",
	SectionAlign8Bytes:          "IMAGE_SCN_ALIGN_8BYTES",
	SectionAlign16Bytes:         "IMAGE_SCN_ALIGN_16BYTES",
	SectionAlign32Bytes:         "IMAGE_SCN_ALIGN_32BYTES",
	SectionAlign64Bytes:         "IMAGE_SCN_ALIGN_64BYTES",
	SectionAlign128Bytes:        "IMAGE_SCN_ALIGN_128BYTES",
	SectionAlign256Bytes:        "IMAGE_SCN_ALIGN_256BYTES",
	SectionAlign512Bytes:        "IMAGE_SCN_ALIGN_512BYTES",
	SectionAlign1024Bytes:       "IMAGE_SCN_ALIGN_1024BYTES",
	SectionAlign2048Bytes:       "IMAGE_SCN_ALIGN_2048BYTES",
	SectionAlign4096Bytes:       "IMAGE_SCN_ALIGN_4096BYTES",
	SectionAlign8192Bytes:       "IMAGE_SCN_ALIGN_8192BYTES",
	SectionLnkNRelocOvfl:        "IMAGE_SCN_LNK_NRELOC_OVFL",
	SectionMemDiscardable:       "IMAGE_SCN_MEM_DISCARDABLE",
	SectionMemNotCached:         "IMAGE_SCN_MEM_NOT_CACHED",
	SectionMemNotPaged:          "IMAGE_SCN_MEM_NOT_PAGED",
	SectionMemShared:            "IMAGE_SCN_MEM_SHARED",
	SectionMemExecute:           "IMAGE_SCN_MEM_EXECUTE",
	SectionMemRead:              "IMAGE_SCN_MEM_READ",
	SectionMemWrite:             "IMAGE_SCN_MEM_WRITE",
}

// Subsystem types
type SubsystemType uint16

const (
	SubsystemUnknown                SubsystemType = 0
	SubsystemNative                 SubsystemType = 1
	SubsystemWindowsGUI             SubsystemType = 2
	SubsystemWindowsCUI             SubsystemType = 3
	SubsystemOS2CUI                 SubsystemType = 5
	SubsystemPosixCUI               SubsystemType = 7
	SubsystemNativeWindows          SubsystemType = 8
	SubsystemWindowsCEGUI           SubsystemType = 9
	SubsystemEFIApplication         SubsystemType = 10
	SubsystemEFIBootServiceDriver   SubsystemType = 11
	SubsystemEFIRuntimeDriver       SubsystemType = 12
	SubsystemEFIROM                 SubsystemType = 13
	SubsystemXbox                   SubsystemType = 14
	SubsystemWindowsBootApplication SubsystemType = 16
)

// SubsystemTypeNames maps subsystem types to their string names
var SubsystemTypeNames = map[SubsystemType]string{
	SubsystemUnknown:                "IMAGE_SUBSYSTEM_UNKNOWN",
	SubsystemNative:                 "IMAGE_SUBSYSTEM_NATIVE",
	SubsystemWindowsGUI:             "IMAGE_SUBSYSTEM_WINDOWS_GUI",
	SubsystemWindowsCUI:             "IMAGE_SUBSYSTEM_WINDOWS_CUI",
	SubsystemOS2CUI:                 "IMAGE_SUBSYSTEM_OS2_CUI",
	SubsystemPosixCUI:               "IMAGE_SUBSYSTEM_POSIX_CUI",
	SubsystemNativeWindows:          "IMAGE_SUBSYSTEM_NATIVE_WINDOWS",
	SubsystemWindowsCEGUI:           "IMAGE_SUBSYSTEM_WINDOWS_CE_GUI",
	SubsystemEFIApplication:         "IMAGE_SUBSYSTEM_EFI_APPLICATION",
	SubsystemEFIBootServiceDriver:   "IMAGE_SUBSYSTEM_EFI_BOOT_SERVICE_DRIVER",
	SubsystemEFIRuntimeDriver:       "IMAGE_SUBSYSTEM_EFI_RUNTIME_DRIVER",
	SubsystemEFIROM:                 "IMAGE_SUBSYSTEM_EFI_ROM",
	SubsystemXbox:                   "IMAGE_SUBSYSTEM_XBOX",
	SubsystemWindowsBootApplication: "IMAGE_SUBSYSTEM_WINDOWS_BOOT_APPLICATION",
}

// Section characteristics flags
type SectionCharacteristic uint32

const (
	SectionTypeReg              SectionCharacteristic = 0x00000000 // reserved
	SectionTypeDSect            SectionCharacteristic = 0x00000001 // reserved
	SectionTypeNoLoad           SectionCharacteristic = 0x00000002 // reserved
	SectionTypeGroup            SectionCharacteristic = 0x00000004 // reserved
	SectionTypeNoPad            SectionCharacteristic = 0x00000008 // reserved
	SectionTypeCopy             SectionCharacteristic = 0x00000010 // reserved
	SectionCntCode              SectionCharacteristic = 0x00000020
	SectionCntInitializedData   SectionCharacteristic = 0x00000040
	SectionCntUninitializedData SectionCharacteristic = 0x00000080
	SectionLnkOther             SectionCharacteristic = 0x00000100
	SectionLnkInfo              SectionCharacteristic = 0x00000200
	SectionLnkOver              SectionCharacteristic = 0x00000400 // reserved
	SectionLnkRemove            SectionCharacteristic = 0x00000800
	SectionLnkComdat            SectionCharacteristic = 0x00001000
	SectionMemProtected         SectionCharacteristic = 0x00004000 // obsolete
	SectionNoDeferSpecExc       SectionCharacteristic = 0x00004000
	SectionGPRel                SectionCharacteristic = 0x00008000
	SectionMemFarData           SectionCharacteristic = 0x00008000
	SectionMemSysHeap           SectionCharacteristic = 0x00010000 // obsolete
	SectionMemPurgeable         SectionCharacteristic = 0x00020000
	SectionMem16Bit             SectionCharacteristic = 0x00020000
	SectionMemLocked            SectionCharacteristic = 0x00040000
	SectionMemPreload           SectionCharacteristic = 0x00080000
	SectionAlign1Bytes          SectionCharacteristic = 0x00100000
	SectionAlign2Bytes          SectionCharacteristic = 0x00200000
	SectionAlign4Bytes          SectionCharacteristic = 0x00300000
	SectionAlign8Bytes          SectionCharacteristic = 0x00400000
	SectionAlign16Bytes         SectionCharacteristic = 0x00500000 // default alignment
	SectionAlign32Bytes         SectionCharacteristic = 0x00600000
	SectionAlign64Bytes         SectionCharacteristic = 0x00700000
	SectionAlign128Bytes        SectionCharacteristic = 0x00800000
	SectionAlign256Bytes        SectionCharacteristic = 0x00900000
	SectionAlign512Bytes        SectionCharacteristic = 0x00A00000
	SectionAlign1024Bytes       SectionCharacteristic = 0x00B00000
	SectionAlign2048Bytes       SectionCharacteristic = 0x00C00000
	SectionAlign4096Bytes       SectionCharacteristic = 0x00D00000
	SectionAlign8192Bytes       SectionCharacteristic = 0x00E00000
	SectionAlignMask            SectionCharacteristic = 0x00F00000
	SectionLnkNRelocOvfl        SectionCharacteristic = 0x01000000
	SectionMemDiscardable       SectionCharacteristic = 0x02000000
	SectionMemNotCached         SectionCharacteristic = 0x04000000
	SectionMemNotPaged          SectionCharacteristic = 0x08000000
	SectionMemShared            SectionCharacteristic = 0x10000000
	SectionMemExecute           SectionCharacteristic = 0x20000000
	SectionMemRead              SectionCharacteristic = 0x40000000
	SectionMemWrite             SectionCharacteristic = 0x80000000
)

// Debug types
type DebugType uint32

const (
	DebugTypeUnknown              DebugType = 0
	DebugTypeCOFF                 DebugType = 1
	DebugTypeCodeView             DebugType = 2
	DebugTypeFPO                  DebugType = 3
	DebugTypeMisc                 DebugType = 4
	DebugTypeException            DebugType = 5
	DebugTypeFixup                DebugType = 6
	DebugTypeOmapToSrc            DebugType = 7
	DebugTypeOmapFromSrc          DebugType = 8
	DebugTypeBorland              DebugType = 9
	DebugTypeReserved10           DebugType = 10
	DebugTypeCLSID                DebugType = 11
	DebugTypeVCFeature            DebugType = 12
	DebugTypePogo                 DebugType = 13
	DebugTypeILTCG                DebugType = 14
	DebugTypeMPX                  DebugType = 15
	DebugTypeRepro                DebugType = 16
	DebugTypeExDLLCharacteristics DebugType = 20
)

// DebugTypeNames maps debug types to their string names
var DebugTypeNames = map[DebugType]string{
	DebugTypeUnknown:              "IMAGE_DEBUG_TYPE_UNKNOWN",
	DebugTypeCOFF:                 "IMAGE_DEBUG_TYPE_COFF",
	DebugTypeCodeView:             "IMAGE_DEBUG_TYPE_CODEVIEW",
	DebugTypeFPO:                  "IMAGE_DEBUG_TYPE_FPO",
	DebugTypeMisc:                 "IMAGE_DEBUG_TYPE_MISC",
	DebugTypeException:            "IMAGE_DEBUG_TYPE_EXCEPTION",
	DebugTypeFixup:                "IMAGE_DEBUG_TYPE_FIXUP",
	DebugTypeOmapToSrc:            "IMAGE_DEBUG_TYPE_OMAP_TO_SRC",
	DebugTypeOmapFromSrc:          "IMAGE_DEBUG_TYPE_OMAP_FROM_SRC",
	DebugTypeBorland:              "IMAGE_DEBUG_TYPE_BORLAND",
	DebugTypeReserved10:           "IMAGE_DEBUG_TYPE_RESERVED10",
	DebugTypeCLSID:                "IMAGE_DEBUG_TYPE_CLSID",
	DebugTypeVCFeature:            "IMAGE_DEBUG_TYPE_VC_FEATURE",
	DebugTypePogo:                 "IMAGE_DEBUG_TYPE_POGO",
	DebugTypeILTCG:                "IMAGE_DEBUG_TYPE_ILTCG",
	DebugTypeMPX:                  "IMAGE_DEBUG_TYPE_MPX",
	DebugTypeRepro:                "IMAGE_DEBUG_TYPE_REPRO",
	DebugTypeExDLLCharacteristics: "IMAGE_DEBUG_TYPE_EX_DLLCHARACTERISTICS",
}
