package pefile

import (
	"fmt"
)

// DebugDirectory represents the IMAGE_DEBUG_DIRECTORY structure
type DebugDirectory struct {
	BaseStructure
	Characteristics  uint32    // Reserved, must be 0
	TimeDateStamp    uint32    // Time and date stamp
	MajorVersion     uint16    // Major version number
	MinorVersion     uint16    // Minor version number
	Type             DebugType // Debug type
	SizeOfData       uint32    // Size of debug data
	AddressOfRawData uint32    // RVA to debug data
	PointerToRawData uint32    // File pointer to debug data

	// Parsed data
	Data []byte // Debug data
}

// Unpack unpacks the debug directory from binary data
func (dd *DebugDirectory) Unpack(data []byte, offset uint32) error {
	dd.SetFileOffset(offset)
	dd.SetName("IMAGE_DEBUG_DIRECTORY")

	if len(data) < int(offset)+28 { // Debug directory is 28 bytes
		return NewPEFormatError("data too short for debug directory", offset)
	}

	return UnpackBinary(data, offset, dd)
}

// Pack packs the debug directory into binary data
func (dd *DebugDirectory) Pack() ([]byte, error) {
	return PackBinary(dd)
}

// Size returns the size of the debug directory
func (dd *DebugDirectory) Size() int {
	return 28
}

// Dump returns a string representation of the debug directory
func (dd *DebugDirectory) Dump() []string {
	lines := DumpStruct(dd, dd.name, 0)

	// Add debug type name
	if name, ok := DebugTypeNames[dd.Type]; ok {
		lines = append(lines, fmt.Sprintf("  Debug Type: %s", name))
	}

	lines = append(lines, fmt.Sprintf("  Data Size: %d bytes", dd.SizeOfData))

	if len(dd.Data) > 0 {
		lines = append(lines, fmt.Sprintf("  Data loaded: %d bytes", len(dd.Data)))
	}

	return lines
}

// DumpDict returns a dictionary representation of the debug directory
func (dd *DebugDirectory) DumpDict() map[string]interface{} {
	dict := DumpStructDict(dd)

	// Add debug type name
	if name, ok := DebugTypeNames[dd.Type]; ok {
		dict["DebugTypeName"] = name
	}

	dict["DataSize"] = dd.SizeOfData
	dict["DataLoaded"] = len(dd.Data) > 0

	return dict
}

// GetDebugData returns the debug data
func (dd *DebugDirectory) GetDebugData(pe *PE) ([]byte, error) {
	if pe == nil {
		return nil, NewPEFormatError("no PE reference available", 0)
	}

	if dd.SizeOfData == 0 {
		return []byte{}, nil
	}

	// Try RVA first, then file offset
	if dd.AddressOfRawData != 0 {
		data, err := pe.GetDataAtRVA(dd.AddressOfRawData, dd.SizeOfData)
		if err == nil {
			return data, nil
		}
	}

	if dd.PointerToRawData != 0 {
		return pe.GetDataAtOffset(dd.PointerToRawData, dd.SizeOfData)
	}

	return nil, NewPEFormatError("no valid debug data pointer", 0)
}
