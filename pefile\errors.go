package pefile

import (
	"errors"
	"fmt"
)

// Common errors
var (
	ErrInvalidPEFile     = errors.New("invalid PE file")
	ErrInvalidDOSHeader  = errors.New("invalid DOS header")
	ErrInvalidNTHeader   = errors.New("invalid NT header")
	ErrInvalidSection    = errors.New("invalid section")
	ErrInvalidImport     = errors.New("invalid import")
	ErrInvalidExport     = errors.New("invalid export")
	ErrInvalidResource   = errors.New("invalid resource")
	ErrInvalidDebugInfo  = errors.New("invalid debug information")
	ErrInvalidRelocation = errors.New("invalid relocation")
	ErrDataTooShort      = errors.New("data too short")
	ErrOffsetOutOfBounds = errors.New("offset out of bounds")
	ErrRVAOutOfBounds    = errors.New("RVA out of bounds")
)

// PEFormatError represents a PE file format error
type PEFormatError struct {
	Message string
	Offset  uint32
	Err     error
}

func (e *PEFormatError) Error() string {
	if e.Offset > 0 {
		return fmt.Sprintf("PE format error at offset 0x%x: %s", e.Offset, e.Message)
	}
	return fmt.Sprintf("PE format error: %s", e.Message)
}

func (e *PEFormatError) Unwrap() error {
	return e.Err
}

// NewPEFormatError creates a new PE format error
func NewPEFormatError(message string, offset uint32) *PEFormatError {
	return &PEFormatError{
		Message: message,
		Offset:  offset,
	}
}

// NewPEFormatErrorWithCause creates a new PE format error with an underlying cause
func NewPEFormatErrorWithCause(message string, offset uint32, err error) *PEFormatError {
	return &PEFormatError{
		Message: message,
		Offset:  offset,
		Err:     err,
	}
}

// Warning represents a parsing warning
type Warning struct {
	Message string
	Offset  uint32
}

func (w Warning) String() string {
	if w.Offset > 0 {
		return fmt.Sprintf("Warning at offset 0x%x: %s", w.Offset, w.Message)
	}
	return fmt.Sprintf("Warning: %s", w.Message)
}

// WarningList holds a collection of warnings
type WarningList []Warning

// Add adds a warning to the list
func (wl *WarningList) Add(message string, offset uint32) {
	*wl = append(*wl, Warning{Message: message, Offset: offset})
}

// AddString adds a warning with just a message
func (wl *WarningList) AddString(message string) {
	*wl = append(*wl, Warning{Message: message})
}

// Len returns the number of warnings
func (wl WarningList) Len() int {
	return len(wl)
}

// Strings returns all warnings as strings
func (wl WarningList) Strings() []string {
	result := make([]string, len(wl))
	for i, w := range wl {
		result[i] = w.String()
	}
	return result
}

// AddWithContext adds a warning with additional context information
func (wl *WarningList) AddWithContext(message string, offset uint32, context map[string]interface{}) {
	warning := Warning{
		Message: message,
		Offset:  offset,
	}

	// Add context information to the message
	if len(context) > 0 {
		contextStr := ""
		for key, value := range context {
			if contextStr != "" {
				contextStr += ", "
			}
			contextStr += fmt.Sprintf("%s=%v", key, value)
		}
		warning.Message = fmt.Sprintf("%s (%s)", message, contextStr)
	}

	*wl = append(*wl, warning)
}

// AddError adds an error as a warning
func (wl *WarningList) AddError(err error, offset uint32) {
	if err != nil {
		wl.Add(fmt.Sprintf("Error: %v", err), offset)
	}
}

// HasWarnings returns true if there are any warnings
func (wl WarningList) HasWarnings() bool {
	return len(wl) > 0
}

// Clear removes all warnings
func (wl *WarningList) Clear() {
	*wl = (*wl)[:0]
}

// GetByOffset returns warnings at a specific offset
func (wl WarningList) GetByOffset(offset uint32) []Warning {
	var result []Warning
	for _, warning := range wl {
		if warning.Offset == offset {
			result = append(result, warning)
		}
	}
	return result
}

// Filter returns warnings that match a predicate function
func (wl WarningList) Filter(predicate func(Warning) bool) []Warning {
	var result []Warning
	for _, warning := range wl {
		if predicate(warning) {
			result = append(result, warning)
		}
	}
	return result
}

// Contains checks if a warning with the given message exists
func (wl WarningList) Contains(message string) bool {
	for _, warning := range wl {
		if warning.Message == message {
			return true
		}
	}
	return false
}

// GetSummary returns a summary of warnings by type
func (wl WarningList) GetSummary() map[string]int {
	summary := make(map[string]int)
	for _, warning := range wl {
		// Extract warning type from message (first word)
		parts := fmt.Sprintf("%s", warning.Message)
		if len(parts) > 0 {
			warningType := "General"
			if len(parts) > 10 {
				warningType = parts[:10] + "..."
			} else {
				warningType = parts
			}
			summary[warningType]++
		}
	}
	return summary
}
