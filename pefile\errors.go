package pefile

import (
	"errors"
	"fmt"
)

// Common errors
var (
	ErrInvalidPEFile     = errors.New("invalid PE file")
	ErrInvalidDOSHeader  = errors.New("invalid DOS header")
	ErrInvalidNTHeader   = errors.New("invalid NT header")
	ErrInvalidSection    = errors.New("invalid section")
	ErrInvalidImport     = errors.New("invalid import")
	ErrInvalidExport     = errors.New("invalid export")
	ErrInvalidResource   = errors.New("invalid resource")
	ErrInvalidDebugInfo  = errors.New("invalid debug information")
	ErrInvalidRelocation = errors.New("invalid relocation")
	ErrDataTooShort      = errors.New("data too short")
	ErrOffsetOutOfBounds = errors.New("offset out of bounds")
	ErrRVAOutOfBounds    = errors.New("RVA out of bounds")
)

// PEFormatError represents a PE file format error
type PEFormatError struct {
	Message string
	Offset  uint32
	Err     error
}

func (e *PEFormatError) Error() string {
	if e.Offset > 0 {
		return fmt.Sprintf("PE format error at offset 0x%x: %s", e.Offset, e.Message)
	}
	return fmt.Sprintf("PE format error: %s", e.Message)
}

func (e *PEFormatError) Unwrap() error {
	return e.Err
}

// NewPEFormatError creates a new PE format error
func NewPEFormatError(message string, offset uint32) *PEFormatError {
	return &PEFormatError{
		Message: message,
		Offset:  offset,
	}
}

// NewPEFormatErrorWithCause creates a new PE format error with an underlying cause
func NewPEFormatErrorWithCause(message string, offset uint32, err error) *PEFormatError {
	return &PEFormatError{
		Message: message,
		Offset:  offset,
		Err:     err,
	}
}

// Warning represents a parsing warning
type Warning struct {
	Message string
	Offset  uint32
}

func (w Warning) String() string {
	if w.Offset > 0 {
		return fmt.Sprintf("Warning at offset 0x%x: %s", w.Offset, w.Message)
	}
	return fmt.Sprintf("Warning: %s", w.Message)
}

// WarningList holds a collection of warnings
type WarningList []Warning

// Add adds a warning to the list
func (wl *WarningList) Add(message string, offset uint32) {
	*wl = append(*wl, Warning{Message: message, Offset: offset})
}

// AddString adds a warning with just a message
func (wl *WarningList) AddString(message string) {
	*wl = append(*wl, Warning{Message: message})
}

// Len returns the number of warnings
func (wl WarningList) Len() int {
	return len(wl)
}

// Strings returns all warnings as strings
func (wl WarningList) Strings() []string {
	result := make([]string, len(wl))
	for i, w := range wl {
		result[i] = w.String()
	}
	return result
}
