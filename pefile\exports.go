package pefile

import (
	"fmt"
)

// ExportDirectory represents the IMAGE_EXPORT_DIRECTORY structure
type ExportDirectory struct {
	BaseStructure
	Characteristics       uint32 // Reserved, must be 0
	TimeDateStamp         uint32 // Time and date stamp
	MajorVersion          uint16 // Major version number
	MinorVersion          uint16 // Minor version number
	Name                  uint32 // RVA to the name of the DLL
	Base                  uint32 // Starting ordinal number for exports
	NumberOfFunctions     uint32 // Number of entries in the export address table
	NumberOfNames         uint32 // Number of entries in the name pointer table
	AddressOfFunctions    uint32 // RVA to the export address table
	AddressOfNames        uint32 // RVA to the export name pointer table
	AddressOfNameOrdinals uint32 // RVA to the ordinal table

	// Parsed data
	DLLName string        // DLL name
	Exports []*ExportData // Exported symbols
}

// ExportData represents an exported symbol
type ExportData struct {
	pe            *PE    // Reference to parent PE
	ordinal       uint32 // Ordinal number
	address       uint32 // Export address (RVA)
	name          []byte // Symbol name (if exported by name)
	nameOffset    uint32 // File offset of name
	forwarder     []byte // Forwarder string (if forwarded export)
	forwardOffset uint32 // File offset of forwarder string
}

// Unpack unpacks the export directory from binary data
func (ed *ExportDirectory) Unpack(data []byte, offset uint32) error {
	ed.SetFileOffset(offset)
	ed.SetName("IMAGE_EXPORT_DIRECTORY")

	if len(data) < int(offset)+40 { // Export directory is 40 bytes
		return NewPEFormatError("data too short for export directory", offset)
	}

	return UnpackBinary(data, offset, ed)
}

// Pack packs the export directory into binary data
func (ed *ExportDirectory) Pack() ([]byte, error) {
	return PackBinary(ed)
}

// Size returns the size of the export directory
func (ed *ExportDirectory) Size() int {
	return 40
}

// Dump returns a string representation of the export directory
func (ed *ExportDirectory) Dump() []string {
	lines := DumpStruct(ed, ed.name, 0)

	lines = append(lines, fmt.Sprintf("  DLL Name: %s", ed.DLLName))
	lines = append(lines, fmt.Sprintf("  Number of exports: %d", len(ed.Exports)))

	if len(ed.Exports) > 0 {
		lines = append(lines, "  Exports:")
		for i, exp := range ed.Exports {
			if i >= 10 { // Limit output
				lines = append(lines, fmt.Sprintf("    ... and %d more", len(ed.Exports)-i))
				break
			}

			if len(exp.name) > 0 {
				if len(exp.forwarder) > 0 {
					lines = append(lines, fmt.Sprintf("    %s (ord %d) -> %s", string(exp.name), exp.ordinal, string(exp.forwarder)))
				} else {
					lines = append(lines, fmt.Sprintf("    %s (ord %d) @ 0x%08X", string(exp.name), exp.ordinal, exp.address))
				}
			} else {
				if len(exp.forwarder) > 0 {
					lines = append(lines, fmt.Sprintf("    ord %d -> %s", exp.ordinal, string(exp.forwarder)))
				} else {
					lines = append(lines, fmt.Sprintf("    ord %d @ 0x%08X", exp.ordinal, exp.address))
				}
			}
		}
	}

	return lines
}

// DumpDict returns a dictionary representation of the export directory
func (ed *ExportDirectory) DumpDict() map[string]interface{} {
	dict := DumpStructDict(ed)
	dict["DLLName"] = ed.DLLName
	dict["NumberOfExports"] = len(ed.Exports)

	var exports []map[string]interface{}
	for _, exp := range ed.Exports {
		expDict := map[string]interface{}{
			"Ordinal": exp.ordinal,
			"Address": exp.address,
		}

		if len(exp.name) > 0 {
			expDict["Name"] = string(exp.name)
			expDict["NameOffset"] = exp.nameOffset
		}

		if len(exp.forwarder) > 0 {
			expDict["Forwarder"] = string(exp.forwarder)
			expDict["ForwardOffset"] = exp.forwardOffset
		}

		exports = append(exports, expDict)
	}
	dict["Exports"] = exports

	return dict
}

// GetName returns the symbol name as a string
func (exp *ExportData) GetName() string {
	if len(exp.name) > 0 {
		return string(TrimNullBytes(exp.name))
	}
	return fmt.Sprintf("ord%d", exp.ordinal)
}

// GetOrdinal returns the ordinal number
func (exp *ExportData) GetOrdinal() uint32 {
	return exp.ordinal
}

// GetAddress returns the export address (RVA)
func (exp *ExportData) GetAddress() uint32 {
	return exp.address
}

// IsForwarded returns true if this is a forwarded export
func (exp *ExportData) IsForwarded() bool {
	return len(exp.forwarder) > 0
}

// GetForwarder returns the forwarder string
func (exp *ExportData) GetForwarder() string {
	if len(exp.forwarder) > 0 {
		return string(TrimNullBytes(exp.forwarder))
	}
	return ""
}

// HasName returns true if this export has a name
func (exp *ExportData) HasName() bool {
	return len(exp.name) > 0
}

// String returns a string representation of the export
func (exp *ExportData) String() string {
	name := exp.GetName()
	if exp.IsForwarded() {
		return fmt.Sprintf("%s (ord %d) -> %s", name, exp.ordinal, exp.GetForwarder())
	}
	return fmt.Sprintf("%s (ord %d) @ 0x%08X", name, exp.ordinal, exp.address)
}
