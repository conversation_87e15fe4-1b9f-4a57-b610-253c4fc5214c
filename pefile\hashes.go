package pefile

import (
	"crypto/md5"
	"crypto/sha1"
	"crypto/sha256"
	"fmt"
	"sort"
	"strings"
)

// HashType represents different hash algorithms
type HashType int

const (
	HashMD5 HashType = iota
	HashSHA1
	HashSHA256
)

// CalculateFileHash calculates the hash of the entire PE file
func (pe *PE) CalculateFileHash(hashType HashType) string {
	data := pe.Data()

	switch hashType {
	case HashMD5:
		hash := md5.Sum(data)
		return fmt.Sprintf("%x", hash)
	case HashSHA1:
		hash := sha1.Sum(data)
		return fmt.Sprintf("%x", hash)
	case HashSHA256:
		hash := sha256.Sum256(data)
		return fmt.Sprintf("%x", hash)
	default:
		return ""
	}
}

// CalculateImportHash calculates the import hash (imphash) of the PE file
// This is a hash of the imported DLLs and functions in a normalized format
func (pe *PE) CalculateImportHash() string {
	if len(pe.Imports) == 0 {
		return ""
	}

	var imports []string

	for _, imp := range pe.Imports {
		dllName := strings.ToLower(imp.DLL)

		// Remove file extension if present
		if strings.HasSuffix(dllName, ".dll") {
			dllName = dllName[:len(dllName)-4]
		}

		for _, symbol := range imp.Imports {
			var funcName string

			if symbol.IsImportByOrdinal() {
				// Try ordinal lookup first
				if symbolName := OrdLookup(imp.DLL, symbol.GetOrdinal(), true); symbolName != "" {
					funcName = strings.ToLower(symbolName)
				} else {
					funcName = fmt.Sprintf("ord%d", symbol.GetOrdinal())
				}
			} else {
				funcName = strings.ToLower(symbol.GetName())
			}

			imports = append(imports, fmt.Sprintf("%s.%s", dllName, funcName))
		}
	}

	// Sort imports for consistent hash
	sort.Strings(imports)

	// Join with commas and calculate MD5
	importString := strings.Join(imports, ",")
	hash := md5.Sum([]byte(importString))
	return fmt.Sprintf("%x", hash)
}

// CalculateExportHash calculates the export hash (exphash) of the PE file
// This is a hash of the exported functions in a normalized format
func (pe *PE) CalculateExportHash() string {
	if pe.ExportDirectory == nil || len(pe.ExportDirectory.Exports) == 0 {
		return ""
	}

	var exports []string

	for _, exp := range pe.ExportDirectory.Exports {
		if exp.HasName() {
			exports = append(exports, strings.ToLower(exp.GetName()))
		} else {
			exports = append(exports, fmt.Sprintf("ord%d", exp.GetOrdinal()))
		}
	}

	// Sort exports for consistent hash
	sort.Strings(exports)

	// Join with commas and calculate MD5
	exportString := strings.Join(exports, ",")
	hash := md5.Sum([]byte(exportString))
	return fmt.Sprintf("%x", hash)
}

// CalculateSectionHash calculates the hash of a specific section
func (pe *PE) CalculateSectionHash(sectionName string, hashType HashType) (string, error) {
	var targetSection *Section

	for _, section := range pe.Sections {
		if section != nil && section.GetName() == sectionName {
			targetSection = section
			break
		}
	}

	if targetSection == nil {
		return "", fmt.Errorf("section %s not found", sectionName)
	}

	data, err := targetSection.GetData()
	if err != nil {
		return "", fmt.Errorf("failed to get section data: %w", err)
	}

	switch hashType {
	case HashMD5:
		hash := md5.Sum(data)
		return fmt.Sprintf("%x", hash), nil
	case HashSHA1:
		hash := sha1.Sum(data)
		return fmt.Sprintf("%x", hash), nil
	case HashSHA256:
		hash := sha256.Sum256(data)
		return fmt.Sprintf("%x", hash), nil
	default:
		return "", fmt.Errorf("unsupported hash type")
	}
}

// CalculateAuthenticodeHash calculates the Authenticode hash
// This excludes the checksum field and certificate table from the hash
func (pe *PE) CalculateAuthenticodeHash(hashType HashType) (string, error) {
	data := make([]byte, len(pe.data))
	copy(data, pe.data)

	// Zero out the checksum field in the optional header
	var checksumOffset uint32
	if pe.OptionalHeader32 != nil {
		checksumOffset = pe.DOSHeader.NewHeaderAddr + 4 + 20 + 64 // NT sig + file header + checksum offset in opt header
	} else if pe.OptionalHeader64 != nil {
		checksumOffset = pe.DOSHeader.NewHeaderAddr + 4 + 20 + 64 // Same offset for 64-bit
	} else {
		return "", fmt.Errorf("no optional header found")
	}

	if checksumOffset+4 <= uint32(len(data)) {
		// Zero out the 4-byte checksum field
		data[checksumOffset] = 0
		data[checksumOffset+1] = 0
		data[checksumOffset+2] = 0
		data[checksumOffset+3] = 0
	}

	// Zero out the certificate table entry in data directories
	var certTableOffset uint32
	if pe.OptionalHeader32 != nil {
		certTableOffset = pe.DOSHeader.NewHeaderAddr + 4 + 20 + 128 + 4*4 // Security directory offset
	} else if pe.OptionalHeader64 != nil {
		certTableOffset = pe.DOSHeader.NewHeaderAddr + 4 + 20 + 144 + 4*4 // Security directory offset (64-bit)
	}

	if certTableOffset+8 <= uint32(len(data)) {
		// Zero out the 8-byte certificate table entry (RVA + Size)
		for i := uint32(0); i < 8; i++ {
			data[certTableOffset+i] = 0
		}
	}

	switch hashType {
	case HashMD5:
		hash := md5.Sum(data)
		return fmt.Sprintf("%x", hash), nil
	case HashSHA1:
		hash := sha1.Sum(data)
		return fmt.Sprintf("%x", hash), nil
	case HashSHA256:
		hash := sha256.Sum256(data)
		return fmt.Sprintf("%x", hash), nil
	default:
		return "", fmt.Errorf("unsupported hash type")
	}
}

// CalculatePEChecksum calculates the PE checksum
// This is the checksum that should be stored in the optional header
func (pe *PE) CalculatePEChecksum() uint32 {
	data := pe.Data()

	// Get the checksum offset
	var checksumOffset uint32
	if pe.OptionalHeader32 != nil {
		checksumOffset = pe.DOSHeader.NewHeaderAddr + 4 + 20 + 64
	} else if pe.OptionalHeader64 != nil {
		checksumOffset = pe.DOSHeader.NewHeaderAddr + 4 + 20 + 64
	} else {
		return 0
	}

	// Calculate checksum excluding the checksum field itself
	var checksum uint64

	// Process data in 16-bit words
	for i := uint32(0); i < uint32(len(data)); i += 2 {
		// Skip the checksum field
		if i == checksumOffset || i == checksumOffset+2 {
			continue
		}

		var word uint16
		if i+1 < uint32(len(data)) {
			word = uint16(data[i]) | uint16(data[i+1])<<8
		} else {
			word = uint16(data[i])
		}

		checksum += uint64(word)

		// Handle carry
		if checksum > 0xFFFFFFFF {
			checksum = (checksum & 0xFFFFFFFF) + (checksum >> 32)
		}
	}

	// Add file size
	checksum += uint64(len(data))

	// Handle final carry
	if checksum > 0xFFFFFFFF {
		checksum = (checksum & 0xFFFFFFFF) + (checksum >> 32)
	}

	return uint32(checksum & 0xFFFFFFFF)
}

// VerifyChecksum verifies the PE checksum
func (pe *PE) VerifyChecksum() bool {
	var storedChecksum uint32

	if pe.OptionalHeader32 != nil {
		storedChecksum = pe.OptionalHeader32.CheckSum
	} else if pe.OptionalHeader64 != nil {
		storedChecksum = pe.OptionalHeader64.CheckSum
	} else {
		return false
	}

	calculatedChecksum := pe.CalculatePEChecksum()
	return storedChecksum == calculatedChecksum
}

// GetHashSummary returns a summary of various hashes for the PE file
func (pe *PE) GetHashSummary() map[string]string {
	hashes := make(map[string]string)

	// File hashes
	hashes["MD5"] = pe.CalculateFileHash(HashMD5)
	hashes["SHA1"] = pe.CalculateFileHash(HashSHA1)
	hashes["SHA256"] = pe.CalculateFileHash(HashSHA256)

	// Import hash
	if imphash := pe.CalculateImportHash(); imphash != "" {
		hashes["Imphash"] = imphash
	}

	// Export hash
	if exphash := pe.CalculateExportHash(); exphash != "" {
		hashes["Exphash"] = exphash
	}

	// Authenticode hashes
	if authMD5, err := pe.CalculateAuthenticodeHash(HashMD5); err == nil {
		hashes["Authenticode_MD5"] = authMD5
	}
	if authSHA1, err := pe.CalculateAuthenticodeHash(HashSHA1); err == nil {
		hashes["Authenticode_SHA1"] = authSHA1
	}
	if authSHA256, err := pe.CalculateAuthenticodeHash(HashSHA256); err == nil {
		hashes["Authenticode_SHA256"] = authSHA256
	}

	// PE checksum
	hashes["PE_Checksum"] = fmt.Sprintf("%08X", pe.CalculatePEChecksum())
	hashes["Checksum_Valid"] = fmt.Sprintf("%t", pe.VerifyChecksum())

	return hashes
}
