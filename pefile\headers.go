package pefile

import (
	"fmt"
)

// <PERSON>Header represents the IMAGE_DOS_HEADER structure
type DOSHeader struct {
	BaseStructure
	Magic              uint16     // e_magic - Magic number
	BytesOnLastPage    uint16     // e_cblp - Bytes on last page of file
	PagesInFile        uint16     // e_cp - Pages in file
	Relocations        uint16     // e_crlc - Relocations
	SizeOfHeader       uint16     // e_cparhdr - Size of header in paragraphs
	MinExtraParagraphs uint16     // e_minalloc - Minimum extra paragraphs needed
	MaxExtraParagraphs uint16     // e_maxalloc - Maximum extra paragraphs needed
	InitialSS          uint16     // e_ss - Initial relative SS value
	InitialSP          uint16     // e_sp - Initial SP value
	Checksum           uint16     // e_csum - Checksum
	InitialIP          uint16     // e_ip - Initial IP value
	InitialCS          uint16     // e_cs - Initial relative CS value
	RelocTableAddr     uint16     // e_lfarlc - File address of relocation table
	OverlayNumber      uint16     // e_ovno - Overlay number
	Reserved           [4]uint16  // e_res[4] - Reserved words
	OEMIdentifier      uint16     // e_oemid - OEM identifier
	OEMInformation     uint16     // e_oeminfo - OEM information
	Reserved2          [10]uint16 // e_res2[10] - Reserved words
	NewHeaderAddr      uint32     // e_lfanew - File address of new exe header
}

// Unpack unpacks the DOS header from binary data
func (h *DOSHeader) Unpack(data []byte, offset uint32) error {
	h.SetFileOffset(offset)
	h.SetName("IMAGE_DOS_HEADER")

	if len(data) < int(offset)+64 { // DOS header is 64 bytes
		return NewPEFormatError("data too short for DOS header", offset)
	}

	h.allZeroes = IsAllZeroes(data[offset : offset+64])

	return UnpackBinary(data, offset, h)
}

// Pack packs the DOS header into binary data
func (h *DOSHeader) Pack() ([]byte, error) {
	return PackBinary(h)
}

// Size returns the size of the DOS header
func (h *DOSHeader) Size() int {
	return 64
}

// Dump returns a string representation of the DOS header
func (h *DOSHeader) Dump() []string {
	return DumpStruct(h, h.Name(), 0)
}

// DumpDict returns a dictionary representation of the DOS header
func (h *DOSHeader) DumpDict() map[string]interface{} {
	return DumpStructDict(h)
}

// NTHeaders represents the IMAGE_NT_HEADERS structure
type NTHeaders struct {
	BaseStructure
	Signature uint32 // PE signature
}

// Unpack unpacks the NT headers from binary data
func (h *NTHeaders) Unpack(data []byte, offset uint32) error {
	h.SetFileOffset(offset)
	h.SetName("IMAGE_NT_HEADERS")

	if len(data) < int(offset)+4 {
		return NewPEFormatError("data too short for NT headers", offset)
	}

	return UnpackBinary(data, offset, h)
}

// Pack packs the NT headers into binary data
func (h *NTHeaders) Pack() ([]byte, error) {
	return PackBinary(h)
}

// Size returns the size of the NT headers
func (h *NTHeaders) Size() int {
	return 4
}

// Dump returns a string representation of the NT headers
func (h *NTHeaders) Dump() []string {
	return DumpStruct(h, h.Name(), 0)
}

// DumpDict returns a dictionary representation of the NT headers
func (h *NTHeaders) DumpDict() map[string]interface{} {
	return DumpStructDict(h)
}

// FileHeader represents the IMAGE_FILE_HEADER structure
type FileHeader struct {
	BaseStructure
	Machine              MachineType         // Target machine
	NumberOfSections     uint16              // Number of sections
	TimeDateStamp        uint32              // Time/date stamp
	PointerToSymbolTable uint32              // File pointer to symbol table
	NumberOfSymbols      uint32              // Number of symbols
	SizeOfOptionalHeader uint16              // Size of optional header
	Characteristics      ImageCharacteristic // Characteristics
}

// Unpack unpacks the file header from binary data
func (h *FileHeader) Unpack(data []byte, offset uint32) error {
	h.SetFileOffset(offset)
	h.SetName("IMAGE_FILE_HEADER")

	if len(data) < int(offset)+20 { // File header is 20 bytes
		return NewPEFormatError("data too short for file header", offset)
	}

	return UnpackBinary(data, offset, h)
}

// Pack packs the file header into binary data
func (h *FileHeader) Pack() ([]byte, error) {
	return PackBinary(h)
}

// Size returns the size of the file header
func (h *FileHeader) Size() int {
	return 20
}

// Dump returns a string representation of the file header
func (h *FileHeader) Dump() []string {
	lines := DumpStruct(h, h.Name(), 0)

	// Add machine type name
	if name, ok := MachineTypeNames[h.Machine]; ok {
		lines = append(lines, fmt.Sprintf("  Machine Name: %s", name))
	}

	// Add characteristics flags
	lines = append(lines, "  Characteristics:")
	for flag, name := range ImageCharacteristicNames {
		if h.Characteristics&flag != 0 {
			lines = append(lines, fmt.Sprintf("    %s", name))
		}
	}

	return lines
}

// DumpDict returns a dictionary representation of the file header
func (h *FileHeader) DumpDict() map[string]interface{} {
	dict := DumpStructDict(h)

	// Add machine type name
	if name, ok := MachineTypeNames[h.Machine]; ok {
		dict["MachineName"] = name
	}

	// Add characteristics flags
	var flags []string
	for flag, name := range ImageCharacteristicNames {
		if h.Characteristics&flag != 0 {
			flags = append(flags, name)
		}
	}
	dict["CharacteristicFlags"] = flags

	return dict
}

// DataDirectory represents an IMAGE_DATA_DIRECTORY structure
type DataDirectory struct {
	VirtualAddress uint32 // RVA of the data
	Size           uint32 // Size of the data
}

// OptionalHeader32 represents the IMAGE_OPTIONAL_HEADER32 structure
type OptionalHeader32 struct {
	BaseStructure
	Magic                       uint16                                       // Magic number
	MajorLinkerVersion          uint8                                        // Linker major version
	MinorLinkerVersion          uint8                                        // Linker minor version
	SizeOfCode                  uint32                                       // Size of code
	SizeOfInitializedData       uint32                                       // Size of initialized data
	SizeOfUninitializedData     uint32                                       // Size of uninitialized data
	AddressOfEntryPoint         uint32                                       // Entry point RVA
	BaseOfCode                  uint32                                       // Base of code
	BaseOfData                  uint32                                       // Base of data
	ImageBase                   uint32                                       // Image base
	SectionAlignment            uint32                                       // Section alignment
	FileAlignment               uint32                                       // File alignment
	MajorOperatingSystemVersion uint16                                       // OS major version
	MinorOperatingSystemVersion uint16                                       // OS minor version
	MajorImageVersion           uint16                                       // Image major version
	MinorImageVersion           uint16                                       // Image minor version
	MajorSubsystemVersion       uint16                                       // Subsystem major version
	MinorSubsystemVersion       uint16                                       // Subsystem minor version
	Win32VersionValue           uint32                                       // Win32 version value
	SizeOfImage                 uint32                                       // Size of image
	SizeOfHeaders               uint32                                       // Size of headers
	CheckSum                    uint32                                       // Checksum
	Subsystem                   SubsystemType                                // Subsystem
	DllCharacteristics          uint16                                       // DLL characteristics
	SizeOfStackReserve          uint32                                       // Stack reserve size
	SizeOfStackCommit           uint32                                       // Stack commit size
	SizeOfHeapReserve           uint32                                       // Heap reserve size
	SizeOfHeapCommit            uint32                                       // Heap commit size
	LoaderFlags                 uint32                                       // Loader flags
	NumberOfRvaAndSizes         uint32                                       // Number of data directories
	DataDirectories             [ImageNumberOfDirectoryEntries]DataDirectory // Data directories
}

// OptionalHeader64 represents the IMAGE_OPTIONAL_HEADER64 structure
type OptionalHeader64 struct {
	BaseStructure
	Magic                       uint16                                       // Magic number
	MajorLinkerVersion          uint8                                        // Linker major version
	MinorLinkerVersion          uint8                                        // Linker minor version
	SizeOfCode                  uint32                                       // Size of code
	SizeOfInitializedData       uint32                                       // Size of initialized data
	SizeOfUninitializedData     uint32                                       // Size of uninitialized data
	AddressOfEntryPoint         uint32                                       // Entry point RVA
	BaseOfCode                  uint32                                       // Base of code
	ImageBase                   uint64                                       // Image base (64-bit)
	SectionAlignment            uint32                                       // Section alignment
	FileAlignment               uint32                                       // File alignment
	MajorOperatingSystemVersion uint16                                       // OS major version
	MinorOperatingSystemVersion uint16                                       // OS minor version
	MajorImageVersion           uint16                                       // Image major version
	MinorImageVersion           uint16                                       // Image minor version
	MajorSubsystemVersion       uint16                                       // Subsystem major version
	MinorSubsystemVersion       uint16                                       // Subsystem minor version
	Win32VersionValue           uint32                                       // Win32 version value
	SizeOfImage                 uint32                                       // Size of image
	SizeOfHeaders               uint32                                       // Size of headers
	CheckSum                    uint32                                       // Checksum
	Subsystem                   SubsystemType                                // Subsystem
	DllCharacteristics          uint16                                       // DLL characteristics
	SizeOfStackReserve          uint64                                       // Stack reserve size (64-bit)
	SizeOfStackCommit           uint64                                       // Stack commit size (64-bit)
	SizeOfHeapReserve           uint64                                       // Heap reserve size (64-bit)
	SizeOfHeapCommit            uint64                                       // Heap commit size (64-bit)
	LoaderFlags                 uint32                                       // Loader flags
	NumberOfRvaAndSizes         uint32                                       // Number of data directories
	DataDirectories             [ImageNumberOfDirectoryEntries]DataDirectory // Data directories
}

// Unpack unpacks the 32-bit optional header from binary data
func (h *OptionalHeader32) Unpack(data []byte, offset uint32) error {
	h.SetFileOffset(offset)
	h.SetName("IMAGE_OPTIONAL_HEADER32")

	if len(data) < int(offset)+224 { // Optional header 32 is 224 bytes
		return NewPEFormatError("data too short for optional header 32", offset)
	}

	return UnpackBinary(data, offset, h)
}

// Pack packs the 32-bit optional header into binary data
func (h *OptionalHeader32) Pack() ([]byte, error) {
	return PackBinary(h)
}

// Size returns the size of the 32-bit optional header
func (h *OptionalHeader32) Size() int {
	return 224
}

// Dump returns a string representation of the 32-bit optional header
func (h *OptionalHeader32) Dump() []string {
	lines := DumpStruct(h, h.Name(), 0)

	// Add subsystem name
	if name, ok := SubsystemTypeNames[h.Subsystem]; ok {
		lines = append(lines, fmt.Sprintf("  Subsystem Name: %s", name))
	}

	// Add data directories
	lines = append(lines, "  Data Directories:")
	for i, dir := range h.DataDirectories {
		if dir.VirtualAddress != 0 || dir.Size != 0 {
			if name, ok := DirectoryEntryNames[DirectoryEntryType(i)]; ok {
				lines = append(lines, fmt.Sprintf("    %s: RVA=0x%08X Size=0x%08X", name, dir.VirtualAddress, dir.Size))
			}
		}
	}

	return lines
}

// DumpDict returns a dictionary representation of the 32-bit optional header
func (h *OptionalHeader32) DumpDict() map[string]interface{} {
	dict := DumpStructDict(h)

	// Add subsystem name
	if name, ok := SubsystemTypeNames[h.Subsystem]; ok {
		dict["SubsystemName"] = name
	}

	// Add data directories
	dataDirs := make(map[string]map[string]uint32)
	for i, dir := range h.DataDirectories {
		if dir.VirtualAddress != 0 || dir.Size != 0 {
			if name, ok := DirectoryEntryNames[DirectoryEntryType(i)]; ok {
				dataDirs[name] = map[string]uint32{
					"VirtualAddress": dir.VirtualAddress,
					"Size":           dir.Size,
				}
			}
		}
	}
	dict["DataDirectories"] = dataDirs

	return dict
}

// Unpack unpacks the 64-bit optional header from binary data
func (h *OptionalHeader64) Unpack(data []byte, offset uint32) error {
	h.SetFileOffset(offset)
	h.SetName("IMAGE_OPTIONAL_HEADER64")

	if len(data) < int(offset)+240 { // Optional header 64 is 240 bytes
		return NewPEFormatError("data too short for optional header 64", offset)
	}

	return UnpackBinary(data, offset, h)
}

// Pack packs the 64-bit optional header into binary data
func (h *OptionalHeader64) Pack() ([]byte, error) {
	return PackBinary(h)
}

// Size returns the size of the 64-bit optional header
func (h *OptionalHeader64) Size() int {
	return 240
}

// Dump returns a string representation of the 64-bit optional header
func (h *OptionalHeader64) Dump() []string {
	lines := DumpStruct(h, h.Name(), 0)

	// Add subsystem name
	if name, ok := SubsystemTypeNames[h.Subsystem]; ok {
		lines = append(lines, fmt.Sprintf("  Subsystem Name: %s", name))
	}

	// Add data directories
	lines = append(lines, "  Data Directories:")
	for i, dir := range h.DataDirectories {
		if dir.VirtualAddress != 0 || dir.Size != 0 {
			if name, ok := DirectoryEntryNames[DirectoryEntryType(i)]; ok {
				lines = append(lines, fmt.Sprintf("    %s: RVA=0x%08X Size=0x%08X", name, dir.VirtualAddress, dir.Size))
			}
		}
	}

	return lines
}

// DumpDict returns a dictionary representation of the 64-bit optional header
func (h *OptionalHeader64) DumpDict() map[string]interface{} {
	dict := DumpStructDict(h)

	// Add subsystem name
	if name, ok := SubsystemTypeNames[h.Subsystem]; ok {
		dict["SubsystemName"] = name
	}

	// Add data directories
	dataDirs := make(map[string]map[string]uint32)
	for i, dir := range h.DataDirectories {
		if dir.VirtualAddress != 0 || dir.Size != 0 {
			if name, ok := DirectoryEntryNames[DirectoryEntryType(i)]; ok {
				dataDirs[name] = map[string]uint32{
					"VirtualAddress": dir.VirtualAddress,
					"Size":           dir.Size,
				}
			}
		}
	}
	dict["DataDirectories"] = dataDirs

	return dict
}
