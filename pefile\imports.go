package pefile

import (
	"fmt"
)

// ImportDescriptor represents an IMAGE_IMPORT_DESCRIPTOR structure
type ImportDescriptor struct {
	BaseStructure
	OriginalFirstThunk uint32 // RVA to original unbound IAT (PIMAGE_THUNK_DATA)
	TimeDateStamp      uint32 // 0 if not bound, -1 if bound, and real date/time stamp
	ForwarderChain     uint32 // -1 if no forwarders
	Name               uint32 // RVA to imported DLL name
	FirstThunk         uint32 // RVA to IAT (if bound this IAT has actual addresses)

	// Parsed data
	DLL     string        // DLL name
	Imports []*ImportData // Imported symbols
}

// ImportData represents an imported symbol
type ImportData struct {
	pe               *PE       // Reference to parent PE
	structTable      ThunkData // Import lookup table entry
	structIAT        ThunkData // Import address table entry
	importByOrdinal  bool      // True if imported by ordinal
	ordinal          uint16    // Ordinal number (if imported by ordinal)
	ordinalOffset    uint32    // File offset of ordinal
	hint             uint16    // Hint (if imported by name)
	name             []byte    // Symbol name (if imported by name)
	nameOffset       uint32    // File offset of name
	bound            uint32    // Bound import address
	address          uint32    // Import address
	hintNameTableRVA uint32    // RVA of hint/name table entry
	thunkOffset      uint32    // File offset of thunk
	thunkRVA         uint32    // RVA of thunk
}

// ThunkData represents IMAGE_THUNK_DATA structure (32-bit)
// This is actually a union, so all fields occupy the same memory
type ThunkData32 struct {
	BaseStructure
	Value uint32 // The actual 32-bit value (union of ForwarderString, Function, Ordinal, AddressOfData)
}

// ThunkData represents IMAGE_THUNK_DATA structure (64-bit)
// This is actually a union, so all fields occupy the same memory
type ThunkData64 struct {
	BaseStructure
	Value uint64 // The actual 64-bit value (union of ForwarderString, Function, Ordinal, AddressOfData)
}

// ThunkData is a generic interface for thunk data
type ThunkData interface {
	Structure
	GetAddressOfData() uint64
	GetOrdinal() uint64
	GetFunction() uint64
	GetForwarderString() uint64
}

// GetAddressOfData returns the address of data field
func (t *ThunkData32) GetAddressOfData() uint64 {
	return uint64(t.Value)
}

// GetOrdinal returns the ordinal field
func (t *ThunkData32) GetOrdinal() uint64 {
	return uint64(t.Value)
}

// GetFunction returns the function field
func (t *ThunkData32) GetFunction() uint64 {
	return uint64(t.Value)
}

// GetForwarderString returns the forwarder string field
func (t *ThunkData32) GetForwarderString() uint64 {
	return uint64(t.Value)
}

// GetAddressOfData returns the address of data field
func (t *ThunkData64) GetAddressOfData() uint64 {
	return t.Value
}

// GetOrdinal returns the ordinal field
func (t *ThunkData64) GetOrdinal() uint64 {
	return t.Value
}

// GetFunction returns the function field
func (t *ThunkData64) GetFunction() uint64 {
	return t.Value
}

// GetForwarderString returns the forwarder string field
func (t *ThunkData64) GetForwarderString() uint64 {
	return t.Value
}

// Unpack unpacks the 32-bit thunk data from binary data
func (t *ThunkData32) Unpack(data []byte, offset uint32) error {
	t.SetFileOffset(offset)
	t.SetName("IMAGE_THUNK_DATA32")

	if len(data) < int(offset)+4 {
		return NewPEFormatError("data too short for thunk data 32", offset)
	}

	return UnpackBinary(data, offset, t)
}

// Pack packs the 32-bit thunk data into binary data
func (t *ThunkData32) Pack() ([]byte, error) {
	return PackBinary(t)
}

// Size returns the size of the 32-bit thunk data
func (t *ThunkData32) Size() int {
	return 4
}

// Dump returns a string representation of the 32-bit thunk data
func (t *ThunkData32) Dump() []string {
	return DumpStruct(t, t.Name(), 0)
}

// DumpDict returns a dictionary representation of the 32-bit thunk data
func (t *ThunkData32) DumpDict() map[string]interface{} {
	return DumpStructDict(t)
}

// Unpack unpacks the 64-bit thunk data from binary data
func (t *ThunkData64) Unpack(data []byte, offset uint32) error {
	t.SetFileOffset(offset)
	t.SetName("IMAGE_THUNK_DATA64")

	if len(data) < int(offset)+8 {
		return NewPEFormatError("data too short for thunk data 64", offset)
	}

	return UnpackBinary(data, offset, t)
}

// Pack packs the 64-bit thunk data into binary data
func (t *ThunkData64) Pack() ([]byte, error) {
	return PackBinary(t)
}

// Size returns the size of the 64-bit thunk data
func (t *ThunkData64) Size() int {
	return 8
}

// Dump returns a string representation of the 64-bit thunk data
func (t *ThunkData64) Dump() []string {
	return DumpStruct(t, t.Name(), 0)
}

// DumpDict returns a dictionary representation of the 64-bit thunk data
func (t *ThunkData64) DumpDict() map[string]interface{} {
	return DumpStructDict(t)
}

// Unpack unpacks the import descriptor from binary data
func (id *ImportDescriptor) Unpack(data []byte, offset uint32) error {
	id.SetFileOffset(offset)
	id.SetName("IMAGE_IMPORT_DESCRIPTOR")

	if len(data) < int(offset)+20 { // Import descriptor is 20 bytes
		return NewPEFormatError("data too short for import descriptor", offset)
	}

	return UnpackBinary(data, offset, id)
}

// Pack packs the import descriptor into binary data
func (id *ImportDescriptor) Pack() ([]byte, error) {
	return PackBinary(id)
}

// Size returns the size of the import descriptor
func (id *ImportDescriptor) Size() int {
	return 20
}

// Dump returns a string representation of the import descriptor
func (id *ImportDescriptor) Dump() []string {
	lines := DumpStruct(id, id.name, 0)

	lines = append(lines, fmt.Sprintf("  DLL: %s", id.DLL))
	lines = append(lines, fmt.Sprintf("  Number of imports: %d", len(id.Imports)))

	if len(id.Imports) > 0 {
		lines = append(lines, "  Imports:")
		for i, imp := range id.Imports {
			if i >= 10 { // Limit output
				lines = append(lines, fmt.Sprintf("    ... and %d more", len(id.Imports)-i))
				break
			}
			if imp.importByOrdinal {
				lines = append(lines, fmt.Sprintf("    Ordinal %d", imp.ordinal))
			} else {
				lines = append(lines, fmt.Sprintf("    %s (hint: %d)", string(imp.name), imp.hint))
			}
		}
	}

	return lines
}

// DumpDict returns a dictionary representation of the import descriptor
func (id *ImportDescriptor) DumpDict() map[string]interface{} {
	dict := DumpStructDict(id)
	dict["DLL"] = id.DLL
	dict["NumberOfImports"] = len(id.Imports)

	var imports []map[string]interface{}
	for _, imp := range id.Imports {
		impDict := map[string]interface{}{
			"ImportByOrdinal": imp.importByOrdinal,
			"Address":         imp.address,
			"Bound":           imp.bound,
			"ThunkRVA":        imp.thunkRVA,
			"ThunkOffset":     imp.thunkOffset,
		}

		if imp.importByOrdinal {
			impDict["Ordinal"] = imp.ordinal
			impDict["OrdinalOffset"] = imp.ordinalOffset
		} else {
			impDict["Name"] = string(imp.name)
			impDict["Hint"] = imp.hint
			impDict["NameOffset"] = imp.nameOffset
			impDict["HintNameTableRVA"] = imp.hintNameTableRVA
		}

		imports = append(imports, impDict)
	}
	dict["Imports"] = imports

	return dict
}

// GetName returns the symbol name as a string
func (imp *ImportData) GetName() string {
	if imp.importByOrdinal {
		return fmt.Sprintf("ord%d", imp.ordinal)
	}
	return string(TrimNullBytes(imp.name))
}

// IsImportByOrdinal returns true if this import is by ordinal
func (imp *ImportData) IsImportByOrdinal() bool {
	return imp.importByOrdinal
}

// GetOrdinal returns the ordinal number
func (imp *ImportData) GetOrdinal() uint16 {
	return imp.ordinal
}

// GetHint returns the hint value
func (imp *ImportData) GetHint() uint16 {
	return imp.hint
}

// GetAddress returns the import address
func (imp *ImportData) GetAddress() uint32 {
	return imp.address
}

// IsBound returns true if this is a bound import
func (imp *ImportData) IsBound() bool {
	return imp.bound != 0
}

// GetBoundAddress returns the bound import address
func (imp *ImportData) GetBoundAddress() uint32 {
	return imp.bound
}

// String returns a string representation of the import
func (imp *ImportData) String() string {
	if imp.importByOrdinal {
		return fmt.Sprintf("ord%d", imp.ordinal)
	}
	return string(TrimNullBytes(imp.name))
}

// IsValidDOSFilename checks if a filename is a valid DOS filename
func IsValidDOSFilename(filename []byte) bool {
	if len(filename) == 0 {
		return false
	}

	// Check for null termination
	str := BytesToString(filename)
	if len(str) == 0 {
		return false
	}

	// Basic validation - should contain only printable ASCII
	for _, b := range []byte(str) {
		if b < 32 || b > 126 {
			return false
		}
	}

	// Should have a reasonable length
	if len(str) > 255 {
		return false
	}

	return true
}

// IsValidFunctionName checks if a function name is valid
func IsValidFunctionName(name []byte) bool {
	if len(name) == 0 {
		return false
	}

	str := BytesToString(name)
	if len(str) == 0 {
		return false
	}

	// Function names should be printable ASCII
	for _, b := range []byte(str) {
		if b < 32 || b > 126 {
			return false
		}
	}

	// Should have a reasonable length
	if len(str) > MaxSymbolNameLength {
		return false
	}

	return true
}
