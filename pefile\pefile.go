package pefile

import (
	"fmt"
	"io"
	"os"
	"strings"
)

// PE represents a Portable Executable file
type PE struct {
	// File information
	filename string
	data     []byte
	size     uint32

	// Headers
	DOSHeader        *DOSHeader
	NTHeaders        *NTHeaders
	FileHeader       *FileHeader
	OptionalHeader32 *OptionalHeader32
	OptionalHeader64 *OptionalHeader64

	// Sections
	Sections []*Section

	// Data directories (will be implemented in separate files)
	// ExportDirectory    *ExportDirectory
	// ImportDirectory    *ImportDirectory
	// ResourceDirectory  *ResourceDirectory
	// ExceptionDirectory *ExceptionDirectory
	// SecurityDirectory  *SecurityDirectory
	// RelocDirectory     *RelocDirectory
	// DebugDirectory     *DebugDirectory
	// ArchDirectory      *ArchDirectory
	// GlobalPtrDirectory *GlobalPtrDirectory
	// TLSDirectory       *TLSDirectory
	// LoadConfigDirectory *LoadConfigDirectory
	// BoundImportDirectory *BoundImportDirectory
	// IATDirectory       *IATDirectory
	// DelayImportDirectory *DelayImportDirectory
	// COMDescriptorDirectory *COMDescriptorDirectory

	// Parsed data
	Imports         []*ImportDescriptor
	ExportDirectory *ExportDirectory
	// Resources []*ResourceDirEntry

	// Parsing options
	fastLoad bool

	// Warnings and errors
	warnings WarningList

	// Internal state
	structures []Structure
	headerData []byte
}

// NewPE creates a new PE instance from a file
func NewPE(filename string) (*PE, error) {
	return NewPEWithOptions(filename, false)
}

// NewPEWithOptions creates a new PE instance with parsing options
func NewPEWithOptions(filename string, fastLoad bool) (*PE, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	data, err := io.ReadAll(file)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	return NewPEFromData(data, filename, fastLoad)
}

// NewPEFromData creates a new PE instance from byte data
func NewPEFromData(data []byte, filename string, fastLoad bool) (*PE, error) {
	pe := &PE{
		filename: filename,
		data:     data,
		size:     uint32(len(data)),
		fastLoad: fastLoad,
	}

	err := pe.parse()
	if err != nil {
		return nil, err
	}

	return pe, nil
}

// parse parses the PE file structure
func (pe *PE) parse() error {
	// Parse DOS header
	if err := pe.parseDOSHeader(); err != nil {
		return fmt.Errorf("failed to parse DOS header: %w", err)
	}

	// Parse NT headers
	if err := pe.parseNTHeaders(); err != nil {
		return fmt.Errorf("failed to parse NT headers: %w", err)
	}

	// Parse file header
	if err := pe.parseFileHeader(); err != nil {
		return fmt.Errorf("failed to parse file header: %w", err)
	}

	// Parse optional header
	if err := pe.parseOptionalHeader(); err != nil {
		return fmt.Errorf("failed to parse optional header: %w", err)
	}

	// Parse sections
	if err := pe.parseSections(); err != nil {
		return fmt.Errorf("failed to parse sections: %w", err)
	}

	// Parse data directories (unless fast load is enabled)
	if !pe.fastLoad {
		if err := pe.parseDataDirectories(); err != nil {
			pe.warnings.AddString(fmt.Sprintf("failed to parse data directories: %v", err))
		}
	}

	return nil
}

// parseDOSHeader parses the DOS header
func (pe *PE) parseDOSHeader() error {
	if len(pe.data) < 64 {
		return NewPEFormatError("file too small for DOS header", 0)
	}

	pe.DOSHeader = &DOSHeader{}
	if err := pe.DOSHeader.Unpack(pe.data, 0); err != nil {
		return err
	}

	pe.structures = append(pe.structures, pe.DOSHeader)

	// Validate DOS signature
	if pe.DOSHeader.Magic == ImageDOSZMSignature {
		return NewPEFormatError("probably a ZM executable (not a PE file)", 0)
	}
	if pe.DOSHeader.Magic != ImageDOSSignature {
		return NewPEFormatError("DOS header magic not found", 0)
	}

	// Validate e_lfanew
	if pe.DOSHeader.NewHeaderAddr > pe.size {
		return NewPEFormatError("invalid e_lfanew value, probably not a PE file", 0)
	}

	return nil
}

// parseNTHeaders parses the NT headers
func (pe *PE) parseNTHeaders() error {
	offset := pe.DOSHeader.NewHeaderAddr

	if len(pe.data) < int(offset)+4 {
		return NewPEFormatError("file too small for NT headers", offset)
	}

	pe.NTHeaders = &NTHeaders{}
	if err := pe.NTHeaders.Unpack(pe.data, offset); err != nil {
		return err
	}

	pe.structures = append(pe.structures, pe.NTHeaders)

	// Validate NT signature
	if pe.NTHeaders.Signature != ImageNTSignature {
		return NewPEFormatError("NT header signature not found", offset)
	}

	return nil
}

// parseFileHeader parses the file header
func (pe *PE) parseFileHeader() error {
	offset := pe.DOSHeader.NewHeaderAddr + 4 // Skip NT signature

	pe.FileHeader = &FileHeader{}
	if err := pe.FileHeader.Unpack(pe.data, offset); err != nil {
		return err
	}

	pe.structures = append(pe.structures, pe.FileHeader)

	return nil
}

// parseOptionalHeader parses the optional header
func (pe *PE) parseOptionalHeader() error {
	offset := pe.DOSHeader.NewHeaderAddr + 4 + 20 // Skip NT signature + file header

	if pe.FileHeader.SizeOfOptionalHeader == 0 {
		return nil // No optional header
	}

	if len(pe.data) < int(offset)+2 {
		return NewPEFormatError("file too small for optional header magic", offset)
	}

	// Read magic to determine 32-bit vs 64-bit
	magic := uint16(pe.data[offset]) | uint16(pe.data[offset+1])<<8

	switch magic {
	case OptionalHeaderMagicPE:
		// 32-bit PE
		pe.OptionalHeader32 = &OptionalHeader32{}
		if err := pe.OptionalHeader32.Unpack(pe.data, offset); err != nil {
			return err
		}
		pe.structures = append(pe.structures, pe.OptionalHeader32)

	case OptionalHeaderMagicPEPlus:
		// 64-bit PE
		pe.OptionalHeader64 = &OptionalHeader64{}
		if err := pe.OptionalHeader64.Unpack(pe.data, offset); err != nil {
			return err
		}
		pe.structures = append(pe.structures, pe.OptionalHeader64)

	default:
		return NewPEFormatError(fmt.Sprintf("invalid optional header magic: 0x%04X", magic), offset)
	}

	return nil
}

// parseSections parses the section headers
func (pe *PE) parseSections() error {
	offset := pe.DOSHeader.NewHeaderAddr + 4 + 20 + uint32(pe.FileHeader.SizeOfOptionalHeader)

	pe.Sections = make([]*Section, pe.FileHeader.NumberOfSections)

	for i := uint16(0); i < pe.FileHeader.NumberOfSections; i++ {
		if i >= MaxSections {
			pe.warnings.AddString(fmt.Sprintf("too many sections (%d), stopping at %d", pe.FileHeader.NumberOfSections, MaxSections))
			break
		}

		section := &Section{}
		if err := section.UnpackWithPE(pe.data, offset, pe); err != nil {
			pe.warnings.Add(fmt.Sprintf("failed to parse section %d: %v", i, err), offset)
			continue
		}

		pe.Sections[i] = section
		pe.structures = append(pe.structures, section)

		offset += uint32(section.Size())
	}

	return nil
}

// parseDataDirectories parses the data directories
func (pe *PE) parseDataDirectories() error {
	var dataDirectories [ImageNumberOfDirectoryEntries]DataDirectory

	if pe.OptionalHeader32 != nil {
		dataDirectories = pe.OptionalHeader32.DataDirectories
	} else if pe.OptionalHeader64 != nil {
		dataDirectories = pe.OptionalHeader64.DataDirectories
	} else {
		return NewPEFormatError("no optional header found", 0)
	}

	// Parse each data directory
	for i, dir := range dataDirectories {
		if dir.VirtualAddress == 0 || dir.Size == 0 {
			continue
		}

		switch DirectoryEntryType(i) {
		case DirectoryEntryExport:
			// Parse export directory
			if err := pe.parseExportDirectory(dir.VirtualAddress, dir.Size); err != nil {
				pe.warnings.AddString(fmt.Sprintf("failed to parse export directory: %v", err))
			}
		case DirectoryEntryImport:
			// Parse import directory
			if err := pe.parseImportDirectory(dir.VirtualAddress, dir.Size); err != nil {
				pe.warnings.AddString(fmt.Sprintf("failed to parse import directory: %v", err))
			}
		case DirectoryEntryResource:
			// Parse resource directory
			// TODO: Implement resource parsing
		case DirectoryEntryDebug:
			// Parse debug directory
			// TODO: Implement debug parsing
		case DirectoryEntryBaseReloc:
			// Parse relocation directory
			// TODO: Implement relocation parsing
		case DirectoryEntryTLS:
			// Parse TLS directory
			// TODO: Implement TLS parsing
		case DirectoryEntryLoadConfig:
			// Parse load config directory
			// TODO: Implement load config parsing
		case DirectoryEntryDelayImport:
			// Parse delay import directory
			// TODO: Implement delay import parsing
		case DirectoryEntryBoundImport:
			// Parse bound import directory
			// TODO: Implement bound import parsing
		}
	}

	return nil
}

// Close closes any resources associated with the PE file
func (pe *PE) Close() error {
	// Nothing to close for now, but this provides future extensibility
	return nil
}

// Filename returns the filename of the PE file
func (pe *PE) Filename() string {
	return pe.filename
}

// Size returns the size of the PE file in bytes
func (pe *PE) Size() uint32 {
	return pe.size
}

// Data returns the raw PE file data
func (pe *PE) Data() []byte {
	return pe.data
}

// Is64Bit returns true if this is a 64-bit PE file
func (pe *PE) Is64Bit() bool {
	return pe.OptionalHeader64 != nil
}

// Is32Bit returns true if this is a 32-bit PE file
func (pe *PE) Is32Bit() bool {
	return pe.OptionalHeader32 != nil
}

// IsDLL returns true if this PE file is a DLL
func (pe *PE) IsDLL() bool {
	return pe.FileHeader.Characteristics&ImageFileDLL != 0
}

// IsExecutable returns true if this PE file is executable
func (pe *PE) IsExecutable() bool {
	return pe.FileHeader.Characteristics&ImageFileExecutableImage != 0
}

// GetWarnings returns all parsing warnings
func (pe *PE) GetWarnings() []string {
	return pe.warnings.Strings()
}

// GetOffsetFromRVA converts an RVA to a file offset
func (pe *PE) GetOffsetFromRVA(rva uint32) (uint32, error) {

	// Find the section containing this RVA
	for _, section := range pe.Sections {
		if section != nil && section.ContainsRVA(rva) {
			offset, err := section.GetOffsetFromRVA(rva)
			if err == nil {
				return offset, nil
			}
		}
	}

	// If not in any section, check if it's in the headers
	if len(pe.Sections) > 0 {
		// Find the lowest section RVA
		lowestRVA := uint32(0xFFFFFFFF)
		for _, section := range pe.Sections {
			if section != nil && section.VirtualAddress < lowestRVA {
				lowestRVA = section.VirtualAddress
			}
		}

		if rva < lowestRVA {
			// Assume it's in the headers
			return rva, nil
		}
	}

	return 0, NewPEFormatError(fmt.Sprintf("RVA 0x%x not found in any section", rva), rva)
}

// GetRVAFromOffset converts a file offset to an RVA
func (pe *PE) GetRVAFromOffset(offset uint32) (uint32, error) {
	// Find the section containing this offset
	for _, section := range pe.Sections {
		if section != nil && section.ContainsOffset(offset) {
			return section.GetRVAFromOffset(offset)
		}
	}

	// If not in any section, check if it's in the headers
	if len(pe.Sections) > 0 {
		// Find the lowest section offset
		lowestOffset := uint32(0xFFFFFFFF)
		for _, section := range pe.Sections {
			if section != nil && section.PointerToRawData > 0 && section.PointerToRawData < lowestOffset {
				lowestOffset = section.PointerToRawData
			}
		}

		if offset < lowestOffset {
			// Assume it's in the headers
			return offset, nil
		}
	}

	return 0, NewPEFormatError(fmt.Sprintf("offset 0x%x not found in any section", offset), offset)
}

// GetStringAtRVA reads a null-terminated string at the given RVA
func (pe *PE) GetStringAtRVA(rva uint32, maxLength uint32) []byte {
	offset, err := pe.GetOffsetFromRVA(rva)
	if err != nil {
		return nil
	}

	return pe.GetStringAtOffset(offset, maxLength)
}

// GetStringAtOffset reads a null-terminated string at the given file offset
func (pe *PE) GetStringAtOffset(offset uint32, maxLength uint32) []byte {
	if offset >= uint32(len(pe.data)) {
		return nil
	}

	end := offset + maxLength
	if end > uint32(len(pe.data)) {
		end = uint32(len(pe.data))
	}

	// Find the null terminator
	for i := offset; i < end; i++ {
		if pe.data[i] == 0 {
			return pe.data[offset:i]
		}
	}

	// No null terminator found, return up to maxLength
	return pe.data[offset:end]
}

// GetDataAtRVA reads data at the given RVA
func (pe *PE) GetDataAtRVA(rva uint32, length uint32) ([]byte, error) {
	offset, err := pe.GetOffsetFromRVA(rva)
	if err != nil {
		return nil, err
	}

	return pe.GetDataAtOffset(offset, length)
}

// GetDataAtOffset reads data at the given file offset
func (pe *PE) GetDataAtOffset(offset uint32, length uint32) ([]byte, error) {
	if offset >= uint32(len(pe.data)) {
		return nil, NewPEFormatError("offset beyond file end", offset)
	}

	end := offset + length
	if end > uint32(len(pe.data)) {
		return nil, NewPEFormatError("data extends beyond file end", offset)
	}

	return pe.data[offset:end], nil
}

// DumpInfo returns a detailed string representation of the PE file
func (pe *PE) DumpInfo() string {
	var lines []string

	// Add warnings if any
	if pe.warnings.Len() > 0 {
		lines = append(lines, "Parsing Warnings:")
		for _, warning := range pe.warnings.Strings() {
			lines = append(lines, fmt.Sprintf("  %s", warning))
		}
		lines = append(lines, "")
	}

	// Add headers
	if pe.DOSHeader != nil {
		lines = append(lines, pe.DOSHeader.Dump()...)
		lines = append(lines, "")
	}

	if pe.NTHeaders != nil {
		lines = append(lines, pe.NTHeaders.Dump()...)
		lines = append(lines, "")
	}

	if pe.FileHeader != nil {
		lines = append(lines, pe.FileHeader.Dump()...)
		lines = append(lines, "")
	}

	if pe.OptionalHeader32 != nil {
		lines = append(lines, pe.OptionalHeader32.Dump()...)
		lines = append(lines, "")
	}

	if pe.OptionalHeader64 != nil {
		lines = append(lines, pe.OptionalHeader64.Dump()...)
		lines = append(lines, "")
	}

	// Add sections
	if len(pe.Sections) > 0 {
		lines = append(lines, "Sections:")
		for i, section := range pe.Sections {
			if section != nil {
				lines = append(lines, fmt.Sprintf("  Section %d:", i))
				sectionLines := section.Dump()
				for _, line := range sectionLines {
					lines = append(lines, fmt.Sprintf("    %s", line))
				}
			}
		}
	}

	return strings.Join(lines, "\n")
}

// parseImportDirectory parses the import directory
func (pe *PE) parseImportDirectory(rva, size uint32) error {
	if rva == 0 || size == 0 {
		return nil
	}

	offset, err := pe.GetOffsetFromRVA(rva)
	if err != nil {
		return fmt.Errorf("invalid import directory RVA 0x%x: %w", rva, err)
	}

	// Parse import descriptors
	var imports []*ImportDescriptor
	descriptorOffset := offset

	for {
		if descriptorOffset+20 > uint32(len(pe.data)) {
			break
		}

		// Check if this is the null terminator descriptor
		if IsAllZeroes(pe.data[descriptorOffset : descriptorOffset+20]) {
			break
		}

		descriptor := &ImportDescriptor{}
		if err := descriptor.Unpack(pe.data, descriptorOffset); err != nil {
			pe.warnings.Add(fmt.Sprintf("failed to parse import descriptor: %v", err), descriptorOffset)
			break
		}

		// Parse DLL name
		if descriptor.Name != 0 {
			_, err := pe.GetOffsetFromRVA(descriptor.Name)
			if err != nil {
				pe.warnings.Add(fmt.Sprintf("invalid DLL name RVA 0x%x", descriptor.Name), descriptorOffset)
			} else {
				dllName := pe.GetStringAtRVA(descriptor.Name, MaxDLLLength)
				if IsValidDOSFilename(dllName) {
					descriptor.DLL = string(dllName)
				} else {
					descriptor.DLL = "*invalid*"
				}
			}
		}

		// Parse imports for this DLL
		if err := pe.parseImports(descriptor); err != nil {
			pe.warnings.Add(fmt.Sprintf("failed to parse imports for %s: %v", descriptor.DLL, err), descriptorOffset)
		}

		imports = append(imports, descriptor)
		pe.structures = append(pe.structures, descriptor)

		descriptorOffset += 20

		// Safety check to prevent infinite loops
		if len(imports) >= MaxImportSymbols {
			pe.warnings.AddString(fmt.Sprintf("too many import descriptors, stopping at %d", MaxImportSymbols))
			break
		}
	}

	pe.Imports = imports
	return nil
}

// parseImports parses the imports for a single DLL
func (pe *PE) parseImports(descriptor *ImportDescriptor) error {
	if descriptor.OriginalFirstThunk == 0 && descriptor.FirstThunk == 0 {
		return nil
	}

	// Use OriginalFirstThunk if available, otherwise use FirstThunk
	thunkRVA := descriptor.OriginalFirstThunk
	if thunkRVA == 0 {
		thunkRVA = descriptor.FirstThunk
	}

	iatRVA := descriptor.FirstThunk

	var imports []*ImportData
	thunkOffset := uint32(0)
	iatOffset := uint32(0)

	if thunkRVA != 0 {
		var err error
		thunkOffset, err = pe.GetOffsetFromRVA(thunkRVA)
		if err != nil {
			return fmt.Errorf("invalid thunk RVA 0x%x: %w", thunkRVA, err)
		}
	}

	if iatRVA != 0 {
		var err error
		iatOffset, err = pe.GetOffsetFromRVA(iatRVA)
		if err != nil {
			return fmt.Errorf("invalid IAT RVA 0x%x: %w", iatRVA, err)
		}
	}

	ordinalFlag := uint64(ImageOrdinalFlag)
	addressMask := uint64(0xFFFFFFFF)
	thunkSize := 4

	if pe.Is64Bit() {
		ordinalFlag = ImageOrdinalFlag64
		addressMask = 0xFFFFFFFFFFFFFFFF
		thunkSize = 8
	}

	for i := 0; i < MaxImportSymbols; i++ {
		if thunkOffset+uint32(thunkSize) > uint32(len(pe.data)) {
			break
		}

		// Read thunk data
		var thunkData ThunkData
		if pe.Is64Bit() {
			thunk64 := &ThunkData64{}
			if err := thunk64.Unpack(pe.data, thunkOffset); err != nil {
				break
			}
			thunkData = thunk64
		} else {
			thunk32 := &ThunkData32{}
			if err := thunk32.Unpack(pe.data, thunkOffset); err != nil {
				break
			}
			thunkData = thunk32
		}

		// Check for null terminator
		if thunkData.GetAddressOfData() == 0 {
			break
		}

		importData := &ImportData{
			pe:          pe,
			structTable: thunkData,
			thunkOffset: thunkOffset,
			thunkRVA:    thunkRVA + uint32(i*thunkSize),
		}

		// Parse import data
		if thunkData.GetAddressOfData()&ordinalFlag != 0 {
			// Import by ordinal
			importData.importByOrdinal = true
			importData.ordinal = uint16(thunkData.GetAddressOfData() & 0xFFFF)
			importData.ordinalOffset = thunkOffset
		} else {
			// Import by name
			importData.importByOrdinal = false
			hintNameRVA := uint32(thunkData.GetAddressOfData() & addressMask)
			importData.hintNameTableRVA = hintNameRVA

			// Parse hint/name table entry
			hintNameOffset, err := pe.GetOffsetFromRVA(hintNameRVA)
			if err != nil {
				pe.warnings.Add(fmt.Sprintf("invalid hint/name RVA 0x%x", hintNameRVA), thunkOffset)
			} else {
				// Read hint (2 bytes)
				if hintNameOffset+2 <= uint32(len(pe.data)) {
					importData.hint = uint16(pe.data[hintNameOffset]) | uint16(pe.data[hintNameOffset+1])<<8
				}

				// Read name
				name := pe.GetStringAtRVA(hintNameRVA+2, MaxImportNameLength)
				if IsValidFunctionName(name) {
					importData.name = name
					importData.nameOffset = hintNameOffset + 2
				} else {
					importData.name = []byte("*invalid*")
				}
			}
		}

		// Read IAT entry if available
		if iatOffset != 0 && iatOffset+uint32(thunkSize) <= uint32(len(pe.data)) {
			if pe.Is64Bit() {
				iatThunk := &ThunkData64{}
				if err := iatThunk.Unpack(pe.data, iatOffset); err == nil {
					importData.structIAT = iatThunk
					importData.address = uint32(iatThunk.GetAddressOfData())
				}
			} else {
				iatThunk := &ThunkData32{}
				if err := iatThunk.Unpack(pe.data, iatOffset); err == nil {
					importData.structIAT = iatThunk
					importData.address = uint32(iatThunk.GetAddressOfData())
				}
			}
		}

		imports = append(imports, importData)

		thunkOffset += uint32(thunkSize)
		if iatOffset != 0 {
			iatOffset += uint32(thunkSize)
		}
	}

	descriptor.Imports = imports
	return nil
}

// parseExportDirectory parses the export directory
func (pe *PE) parseExportDirectory(rva, size uint32) error {
	if rva == 0 || size == 0 {
		return nil
	}

	offset, err := pe.GetOffsetFromRVA(rva)
	if err != nil {
		return fmt.Errorf("invalid export directory RVA 0x%x: %w", rva, err)
	}

	// Parse export directory structure
	exportDir := &ExportDirectory{}
	if err := exportDir.Unpack(pe.data, offset); err != nil {
		return fmt.Errorf("failed to parse export directory: %w", err)
	}

	pe.structures = append(pe.structures, exportDir)

	// Parse DLL name
	if exportDir.Name != 0 {
		dllName := pe.GetStringAtRVA(exportDir.Name, MaxDLLLength)
		if IsValidDOSFilename(dllName) {
			exportDir.DLLName = string(dllName)
		} else {
			exportDir.DLLName = "*invalid*"
		}
	}

	// Parse exports
	if err := pe.parseExports(exportDir, rva, size); err != nil {
		pe.warnings.AddString(fmt.Sprintf("failed to parse exports: %v", err))
	}

	// Store the export directory
	pe.ExportDirectory = exportDir

	return nil
}

// parseExports parses the export tables
func (pe *PE) parseExports(exportDir *ExportDirectory, exportDirRVA, exportDirSize uint32) error {
	if exportDir.NumberOfFunctions == 0 {
		return nil
	}

	// Safety check
	if exportDir.NumberOfFunctions > MaxSymbolExportCount {
		pe.warnings.AddString(fmt.Sprintf("too many exports (%d), limiting to %d", exportDir.NumberOfFunctions, MaxSymbolExportCount))
		exportDir.NumberOfFunctions = MaxSymbolExportCount
	}

	// Read export address table
	addressTableOffset, err := pe.GetOffsetFromRVA(exportDir.AddressOfFunctions)
	if err != nil {
		return fmt.Errorf("invalid export address table RVA 0x%x: %w", exportDir.AddressOfFunctions, err)
	}

	addressTable := make([]uint32, exportDir.NumberOfFunctions)
	for i := uint32(0); i < exportDir.NumberOfFunctions; i++ {
		if addressTableOffset+i*4+4 > uint32(len(pe.data)) {
			break
		}

		address := uint32(pe.data[addressTableOffset+i*4]) |
			uint32(pe.data[addressTableOffset+i*4+1])<<8 |
			uint32(pe.data[addressTableOffset+i*4+2])<<16 |
			uint32(pe.data[addressTableOffset+i*4+3])<<24

		addressTable[i] = address
	}

	// Read name pointer table (if present)
	var nameTable []uint32
	if exportDir.NumberOfNames > 0 && exportDir.AddressOfNames != 0 {
		nameTableOffset, err := pe.GetOffsetFromRVA(exportDir.AddressOfNames)
		if err != nil {
			pe.warnings.AddString(fmt.Sprintf("invalid export name table RVA 0x%x", exportDir.AddressOfNames))
		} else {
			nameTable = make([]uint32, exportDir.NumberOfNames)
			for i := uint32(0); i < exportDir.NumberOfNames; i++ {
				if nameTableOffset+i*4+4 > uint32(len(pe.data)) {
					break
				}

				nameRVA := uint32(pe.data[nameTableOffset+i*4]) |
					uint32(pe.data[nameTableOffset+i*4+1])<<8 |
					uint32(pe.data[nameTableOffset+i*4+2])<<16 |
					uint32(pe.data[nameTableOffset+i*4+3])<<24

				nameTable[i] = nameRVA
			}
		}
	}

	// Read ordinal table (if present)
	var ordinalTable []uint16
	if exportDir.NumberOfNames > 0 && exportDir.AddressOfNameOrdinals != 0 {
		ordinalTableOffset, err := pe.GetOffsetFromRVA(exportDir.AddressOfNameOrdinals)
		if err != nil {
			pe.warnings.AddString(fmt.Sprintf("invalid export ordinal table RVA 0x%x", exportDir.AddressOfNameOrdinals))
		} else {
			ordinalTable = make([]uint16, exportDir.NumberOfNames)
			for i := uint32(0); i < exportDir.NumberOfNames; i++ {
				if ordinalTableOffset+i*2+2 > uint32(len(pe.data)) {
					break
				}

				ordinal := uint16(pe.data[ordinalTableOffset+i*2]) |
					uint16(pe.data[ordinalTableOffset+i*2+1])<<8

				ordinalTable[i] = ordinal
			}
		}
	}

	// Create export data entries
	var exports []*ExportData

	// Process all functions in the address table
	for i := uint32(0); i < exportDir.NumberOfFunctions; i++ {
		if addressTable[i] == 0 {
			continue // Skip empty entries
		}

		export := &ExportData{
			pe:      pe,
			ordinal: exportDir.Base + i,
			address: addressTable[i],
		}

		// Check if this is a forwarded export
		// Forwarded exports have addresses within the export directory
		if export.address >= exportDirRVA && export.address < exportDirRVA+exportDirSize {
			// This is a forwarded export
			forwarder := pe.GetStringAtRVA(export.address, MaxSymbolNameLength)
			if len(forwarder) > 0 {
				export.forwarder = forwarder
				forwardOffset, err := pe.GetOffsetFromRVA(export.address)
				if err == nil {
					export.forwardOffset = forwardOffset
				}
			}
		}

		// Find the name for this export (if it has one)
		for j := uint32(0); j < uint32(len(ordinalTable)); j++ {
			if uint32(ordinalTable[j]) == i && j < uint32(len(nameTable)) {
				// This export has a name
				name := pe.GetStringAtRVA(nameTable[j], MaxSymbolNameLength)
				if IsValidFunctionName(name) {
					export.name = name
					nameOffset, err := pe.GetOffsetFromRVA(nameTable[j])
					if err == nil {
						export.nameOffset = nameOffset
					}
				}
				break
			}
		}

		exports = append(exports, export)
	}

	exportDir.Exports = exports
	return nil
}
