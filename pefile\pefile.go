package pefile

import (
	"fmt"
	"io"
	"os"
	"strings"
)

// PE represents a Portable Executable file
type PE struct {
	// File information
	filename string
	data     []byte
	size     uint32

	// Headers
	DOSHeader        *DOSHeader
	NTHeaders        *NTHeaders
	FileHeader       *FileHeader
	OptionalHeader32 *OptionalHeader32
	OptionalHeader64 *OptionalHeader64

	// Sections
	Sections []*Section

	// Data directories (will be implemented in separate files)
	// ExportDirectory    *ExportDirectory
	// ImportDirectory    *ImportDirectory
	// ResourceDirectory  *ResourceDirectory
	// ExceptionDirectory *ExceptionDirectory
	// SecurityDirectory  *SecurityDirectory
	// RelocDirectory     *RelocDirectory
	// DebugDirectory     *DebugDirectory
	// ArchDirectory      *ArchDirectory
	// GlobalPtrDirectory *GlobalPtrDirectory
	// TLSDirectory       *TLSDirectory
	// LoadConfigDirectory *LoadConfigDirectory
	// BoundImportDirectory *BoundImportDirectory
	// IATDirectory       *IATDirectory
	// DelayImportDirectory *DelayImportDirectory
	// COMDescriptorDirectory *COMDescriptorDirectory

	// Parsed data (will be implemented in separate files)
	// Imports  []*ImportDescriptor
	// Exports  []*ExportData
	// Resources []*ResourceDirEntry

	// Parsing options
	fastLoad bool

	// Warnings and errors
	warnings WarningList

	// Internal state
	structures []Structure
	headerData []byte
}

// NewPE creates a new PE instance from a file
func NewPE(filename string) (*PE, error) {
	return NewPEWithOptions(filename, false)
}

// NewPEWithOptions creates a new PE instance with parsing options
func NewPEWithOptions(filename string, fastLoad bool) (*PE, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	data, err := io.ReadAll(file)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	return NewPEFromData(data, filename, fastLoad)
}

// NewPEFromData creates a new PE instance from byte data
func NewPEFromData(data []byte, filename string, fastLoad bool) (*PE, error) {
	pe := &PE{
		filename: filename,
		data:     data,
		size:     uint32(len(data)),
		fastLoad: fastLoad,
	}

	err := pe.parse()
	if err != nil {
		return nil, err
	}

	return pe, nil
}

// parse parses the PE file structure
func (pe *PE) parse() error {
	// Parse DOS header
	if err := pe.parseDOSHeader(); err != nil {
		return fmt.Errorf("failed to parse DOS header: %w", err)
	}

	// Parse NT headers
	if err := pe.parseNTHeaders(); err != nil {
		return fmt.Errorf("failed to parse NT headers: %w", err)
	}

	// Parse file header
	if err := pe.parseFileHeader(); err != nil {
		return fmt.Errorf("failed to parse file header: %w", err)
	}

	// Parse optional header
	if err := pe.parseOptionalHeader(); err != nil {
		return fmt.Errorf("failed to parse optional header: %w", err)
	}

	// Parse sections
	if err := pe.parseSections(); err != nil {
		return fmt.Errorf("failed to parse sections: %w", err)
	}

	// Parse data directories (unless fast load is enabled)
	if !pe.fastLoad {
		if err := pe.parseDataDirectories(); err != nil {
			pe.warnings.AddString(fmt.Sprintf("failed to parse data directories: %v", err))
		}
	}

	return nil
}

// parseDOSHeader parses the DOS header
func (pe *PE) parseDOSHeader() error {
	if len(pe.data) < 64 {
		return NewPEFormatError("file too small for DOS header", 0)
	}

	pe.DOSHeader = &DOSHeader{}
	if err := pe.DOSHeader.Unpack(pe.data, 0); err != nil {
		return err
	}

	pe.structures = append(pe.structures, pe.DOSHeader)

	// Validate DOS signature
	if pe.DOSHeader.Magic == ImageDOSZMSignature {
		return NewPEFormatError("probably a ZM executable (not a PE file)", 0)
	}
	if pe.DOSHeader.Magic != ImageDOSSignature {
		return NewPEFormatError("DOS header magic not found", 0)
	}

	// Validate e_lfanew
	if pe.DOSHeader.NewHeaderAddr > pe.size {
		return NewPEFormatError("invalid e_lfanew value, probably not a PE file", 0)
	}

	return nil
}

// parseNTHeaders parses the NT headers
func (pe *PE) parseNTHeaders() error {
	offset := pe.DOSHeader.NewHeaderAddr

	if len(pe.data) < int(offset)+4 {
		return NewPEFormatError("file too small for NT headers", offset)
	}

	pe.NTHeaders = &NTHeaders{}
	if err := pe.NTHeaders.Unpack(pe.data, offset); err != nil {
		return err
	}

	pe.structures = append(pe.structures, pe.NTHeaders)

	// Validate NT signature
	if pe.NTHeaders.Signature != ImageNTSignature {
		return NewPEFormatError("NT header signature not found", offset)
	}

	return nil
}

// parseFileHeader parses the file header
func (pe *PE) parseFileHeader() error {
	offset := pe.DOSHeader.NewHeaderAddr + 4 // Skip NT signature

	pe.FileHeader = &FileHeader{}
	if err := pe.FileHeader.Unpack(pe.data, offset); err != nil {
		return err
	}

	pe.structures = append(pe.structures, pe.FileHeader)

	return nil
}

// parseOptionalHeader parses the optional header
func (pe *PE) parseOptionalHeader() error {
	offset := pe.DOSHeader.NewHeaderAddr + 4 + 20 // Skip NT signature + file header

	if pe.FileHeader.SizeOfOptionalHeader == 0 {
		return nil // No optional header
	}

	if len(pe.data) < int(offset)+2 {
		return NewPEFormatError("file too small for optional header magic", offset)
	}

	// Read magic to determine 32-bit vs 64-bit
	magic := uint16(pe.data[offset]) | uint16(pe.data[offset+1])<<8

	switch magic {
	case OptionalHeaderMagicPE:
		// 32-bit PE
		pe.OptionalHeader32 = &OptionalHeader32{}
		if err := pe.OptionalHeader32.Unpack(pe.data, offset); err != nil {
			return err
		}
		pe.structures = append(pe.structures, pe.OptionalHeader32)

	case OptionalHeaderMagicPEPlus:
		// 64-bit PE
		pe.OptionalHeader64 = &OptionalHeader64{}
		if err := pe.OptionalHeader64.Unpack(pe.data, offset); err != nil {
			return err
		}
		pe.structures = append(pe.structures, pe.OptionalHeader64)

	default:
		return NewPEFormatError(fmt.Sprintf("invalid optional header magic: 0x%04X", magic), offset)
	}

	return nil
}

// parseSections parses the section headers
func (pe *PE) parseSections() error {
	offset := pe.DOSHeader.NewHeaderAddr + 4 + 20 + uint32(pe.FileHeader.SizeOfOptionalHeader)

	pe.Sections = make([]*Section, pe.FileHeader.NumberOfSections)

	for i := uint16(0); i < pe.FileHeader.NumberOfSections; i++ {
		if i >= MaxSections {
			pe.warnings.AddString(fmt.Sprintf("too many sections (%d), stopping at %d", pe.FileHeader.NumberOfSections, MaxSections))
			break
		}

		section := &Section{}
		if err := section.UnpackWithPE(pe.data, offset, pe); err != nil {
			pe.warnings.Add(fmt.Sprintf("failed to parse section %d: %v", i, err), offset)
			continue
		}

		pe.Sections[i] = section
		pe.structures = append(pe.structures, section)

		offset += uint32(section.Size())
	}

	return nil
}

// parseDataDirectories parses the data directories
func (pe *PE) parseDataDirectories() error {
	var dataDirectories [ImageNumberOfDirectoryEntries]DataDirectory

	if pe.OptionalHeader32 != nil {
		dataDirectories = pe.OptionalHeader32.DataDirectories
	} else if pe.OptionalHeader64 != nil {
		dataDirectories = pe.OptionalHeader64.DataDirectories
	} else {
		return NewPEFormatError("no optional header found", 0)
	}

	// Parse each data directory
	for i, dir := range dataDirectories {
		if dir.VirtualAddress == 0 || dir.Size == 0 {
			continue
		}

		switch DirectoryEntryType(i) {
		case DirectoryEntryExport:
			// Parse export directory
			// TODO: Implement export parsing
		case DirectoryEntryImport:
			// Parse import directory
			// TODO: Implement import parsing
		case DirectoryEntryResource:
			// Parse resource directory
			// TODO: Implement resource parsing
		case DirectoryEntryDebug:
			// Parse debug directory
			// TODO: Implement debug parsing
		case DirectoryEntryBaseReloc:
			// Parse relocation directory
			// TODO: Implement relocation parsing
		case DirectoryEntryTLS:
			// Parse TLS directory
			// TODO: Implement TLS parsing
		case DirectoryEntryLoadConfig:
			// Parse load config directory
			// TODO: Implement load config parsing
		case DirectoryEntryDelayImport:
			// Parse delay import directory
			// TODO: Implement delay import parsing
		case DirectoryEntryBoundImport:
			// Parse bound import directory
			// TODO: Implement bound import parsing
		}
	}

	return nil
}

// Close closes any resources associated with the PE file
func (pe *PE) Close() error {
	// Nothing to close for now, but this provides future extensibility
	return nil
}

// Filename returns the filename of the PE file
func (pe *PE) Filename() string {
	return pe.filename
}

// Size returns the size of the PE file in bytes
func (pe *PE) Size() uint32 {
	return pe.size
}

// Data returns the raw PE file data
func (pe *PE) Data() []byte {
	return pe.data
}

// Is64Bit returns true if this is a 64-bit PE file
func (pe *PE) Is64Bit() bool {
	return pe.OptionalHeader64 != nil
}

// Is32Bit returns true if this is a 32-bit PE file
func (pe *PE) Is32Bit() bool {
	return pe.OptionalHeader32 != nil
}

// IsDLL returns true if this PE file is a DLL
func (pe *PE) IsDLL() bool {
	return pe.FileHeader.Characteristics&ImageFileDLL != 0
}

// IsExecutable returns true if this PE file is executable
func (pe *PE) IsExecutable() bool {
	return pe.FileHeader.Characteristics&ImageFileExecutableImage != 0
}

// GetWarnings returns all parsing warnings
func (pe *PE) GetWarnings() []string {
	return pe.warnings.Strings()
}

// DumpInfo returns a detailed string representation of the PE file
func (pe *PE) DumpInfo() string {
	var lines []string

	// Add warnings if any
	if pe.warnings.Len() > 0 {
		lines = append(lines, "Parsing Warnings:")
		for _, warning := range pe.warnings.Strings() {
			lines = append(lines, fmt.Sprintf("  %s", warning))
		}
		lines = append(lines, "")
	}

	// Add headers
	if pe.DOSHeader != nil {
		lines = append(lines, pe.DOSHeader.Dump()...)
		lines = append(lines, "")
	}

	if pe.NTHeaders != nil {
		lines = append(lines, pe.NTHeaders.Dump()...)
		lines = append(lines, "")
	}

	if pe.FileHeader != nil {
		lines = append(lines, pe.FileHeader.Dump()...)
		lines = append(lines, "")
	}

	if pe.OptionalHeader32 != nil {
		lines = append(lines, pe.OptionalHeader32.Dump()...)
		lines = append(lines, "")
	}

	if pe.OptionalHeader64 != nil {
		lines = append(lines, pe.OptionalHeader64.Dump()...)
		lines = append(lines, "")
	}

	// Add sections
	if len(pe.Sections) > 0 {
		lines = append(lines, "Sections:")
		for i, section := range pe.Sections {
			if section != nil {
				lines = append(lines, fmt.Sprintf("  Section %d:", i))
				sectionLines := section.Dump()
				for _, line := range sectionLines {
					lines = append(lines, fmt.Sprintf("    %s", line))
				}
			}
		}
	}

	return strings.Join(lines, "\n")
}
