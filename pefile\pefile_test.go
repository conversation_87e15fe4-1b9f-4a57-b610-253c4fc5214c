package pefile

import (
	"fmt"
	"testing"
)

// TestDOSHeaderUnpack tests DOS header unpacking
func TestDOSHeaderUnpack(t *testing.T) {
	// Create a minimal DOS header
	data := make([]byte, 64)

	// Set DOS signature "MZ"
	data[0] = 0x4D // 'M'
	data[1] = 0x5A // 'Z'

	// Set e_lfanew to point to offset 0x80
	data[60] = 0x80
	data[61] = 0x00
	data[62] = 0x00
	data[63] = 0x00

	header := &DOSHeader{}
	err := header.Unpack(data, 0)

	if err != nil {
		t.Fatalf("Failed to unpack DOS header: %v", err)
	}

	if header.Magic != ImageDOSSignature {
		t.Errorf("Expected DOS signature 0x%04X, got 0x%04X", ImageDOSSignature, header.Magic)
	}

	if header.NewHeaderAddr != 0x80 {
		t.Errorf("Expected e_lfanew 0x80, got 0x%08X", header.NewHeaderAddr)
	}
}

// TestFileHeaderUnpack tests file header unpacking
func TestFileHeaderUnpack(t *testing.T) {
	// Create a minimal file header
	data := make([]byte, 20)

	// Set machine type to i386
	data[0] = 0x4C
	data[1] = 0x01

	// Set number of sections to 3
	data[2] = 0x03
	data[3] = 0x00

	// Set characteristics to executable image
	data[18] = 0x02
	data[19] = 0x00

	header := &FileHeader{}
	err := header.Unpack(data, 0)

	if err != nil {
		t.Fatalf("Failed to unpack file header: %v", err)
	}

	if header.Machine != MachineI386 {
		t.Errorf("Expected machine type 0x%04X, got 0x%04X", MachineI386, header.Machine)
	}

	if header.NumberOfSections != 3 {
		t.Errorf("Expected 3 sections, got %d", header.NumberOfSections)
	}

	if header.Characteristics&ImageFileExecutableImage == 0 {
		t.Error("Expected executable image characteristic to be set")
	}
}

// TestOptionalHeader32Unpack tests 32-bit optional header unpacking
func TestOptionalHeader32Unpack(t *testing.T) {
	// Create a minimal 32-bit optional header
	data := make([]byte, 224)

	// Set magic to PE32
	data[0] = 0x0B
	data[1] = 0x01

	// Set entry point
	data[16] = 0x00
	data[17] = 0x10
	data[18] = 0x00
	data[19] = 0x00

	// Set image base
	data[28] = 0x00
	data[29] = 0x00
	data[30] = 0x40
	data[31] = 0x00

	// Set subsystem to Windows GUI
	data[68] = 0x02
	data[69] = 0x00

	header := &OptionalHeader32{}
	err := header.Unpack(data, 0)

	if err != nil {
		t.Fatalf("Failed to unpack optional header 32: %v", err)
	}

	if header.Magic != OptionalHeaderMagicPE {
		t.Errorf("Expected magic 0x%04X, got 0x%04X", OptionalHeaderMagicPE, header.Magic)
	}

	if header.AddressOfEntryPoint != 0x1000 {
		t.Errorf("Expected entry point 0x1000, got 0x%08X", header.AddressOfEntryPoint)
	}

	if header.ImageBase != 0x400000 {
		t.Errorf("Expected image base 0x400000, got 0x%08X", header.ImageBase)
	}

	if header.Subsystem != SubsystemWindowsGUI {
		t.Errorf("Expected subsystem %d, got %d", SubsystemWindowsGUI, header.Subsystem)
	}
}

// TestSectionUnpack tests section header unpacking
func TestSectionUnpack(t *testing.T) {
	// Create a minimal section header
	data := make([]byte, 40)

	// Set section name ".text"
	copy(data[0:8], []byte(".text\x00\x00\x00"))

	// Set virtual size
	data[8] = 0x00
	data[9] = 0x10
	data[10] = 0x00
	data[11] = 0x00

	// Set virtual address
	data[12] = 0x00
	data[13] = 0x10
	data[14] = 0x00
	data[15] = 0x00

	// Set size of raw data
	data[16] = 0x00
	data[17] = 0x10
	data[18] = 0x00
	data[19] = 0x00

	// Set pointer to raw data
	data[20] = 0x00
	data[21] = 0x04
	data[22] = 0x00
	data[23] = 0x00

	// Set characteristics (code + execute + read)
	// SectionCntCode (0x20) + SectionMemExecute (0x20000000) + SectionMemRead (0x40000000) = 0x60000020
	data[36] = 0x20
	data[37] = 0x00
	data[38] = 0x00
	data[39] = 0x60

	section := &Section{}
	err := section.UnpackWithPE(data, 0, nil)

	if err != nil {
		t.Fatalf("Failed to unpack section: %v", err)
	}

	if section.GetName() != ".text" {
		t.Errorf("Expected section name '.text', got '%s'", section.GetName())
	}

	if section.VirtualSize != 0x1000 {
		t.Errorf("Expected virtual size 0x1000, got 0x%08X", section.VirtualSize)
	}

	if section.VirtualAddress != 0x1000 {
		t.Errorf("Expected virtual address 0x1000, got 0x%08X", section.VirtualAddress)
	}

	if !section.ContainsCode() {
		t.Error("Expected section to contain code")
	}

	if !section.IsExecutable() {
		t.Error("Expected section to be executable")
	}

	if !section.IsReadable() {
		t.Error("Expected section to be readable")
	}
}

// TestConstants tests that constants are defined correctly
func TestConstants(t *testing.T) {
	if ImageDOSSignature != 0x5A4D {
		t.Errorf("Expected DOS signature 0x5A4D, got 0x%04X", ImageDOSSignature)
	}

	if ImageNTSignature != 0x00004550 {
		t.Errorf("Expected NT signature 0x00004550, got 0x%08X", ImageNTSignature)
	}

	if OptionalHeaderMagicPE != 0x10B {
		t.Errorf("Expected PE32 magic 0x10B, got 0x%04X", OptionalHeaderMagicPE)
	}

	if OptionalHeaderMagicPEPlus != 0x20B {
		t.Errorf("Expected PE32+ magic 0x20B, got 0x%04X", OptionalHeaderMagicPEPlus)
	}
}

// TestUtilityFunctions tests utility functions
func TestUtilityFunctions(t *testing.T) {
	// Test BytesToString
	data := []byte{'H', 'e', 'l', 'l', 'o', 0, 'W', 'o', 'r', 'l', 'd'}
	str := BytesToString(data)
	if str != "Hello" {
		t.Errorf("Expected 'Hello', got '%s'", str)
	}

	// Test AlignUp
	aligned := AlignUp(0x123, 0x1000)
	if aligned != 0x1000 {
		t.Errorf("Expected 0x1000, got 0x%08X", aligned)
	}

	aligned = AlignUp(0x1000, 0x1000)
	if aligned != 0x1000 {
		t.Errorf("Expected 0x1000, got 0x%08X", aligned)
	}

	aligned = AlignUp(0x1001, 0x1000)
	if aligned != 0x2000 {
		t.Errorf("Expected 0x2000, got 0x%08X", aligned)
	}

	// Test AlignDown
	aligned = AlignDown(0x1234, 0x1000)
	if aligned != 0x1000 {
		t.Errorf("Expected 0x1000, got 0x%08X", aligned)
	}

	aligned = AlignDown(0x1000, 0x1000)
	if aligned != 0x1000 {
		t.Errorf("Expected 0x1000, got 0x%08X", aligned)
	}

	// Test IsAllZeroes
	zeros := make([]byte, 10)
	if !IsAllZeroes(zeros) {
		t.Error("Expected all zeroes to be detected")
	}

	zeros[5] = 1
	if IsAllZeroes(zeros) {
		t.Error("Expected non-zero byte to be detected")
	}
}

// TestImportDescriptorUnpack tests import descriptor unpacking
func TestImportDescriptorUnpack(t *testing.T) {
	// Create a minimal import descriptor
	data := make([]byte, 20)

	// Set OriginalFirstThunk
	data[0] = 0x00
	data[1] = 0x20
	data[2] = 0x00
	data[3] = 0x00

	// Set TimeDateStamp
	data[4] = 0x00
	data[5] = 0x00
	data[6] = 0x00
	data[7] = 0x00

	// Set ForwarderChain
	data[8] = 0xFF
	data[9] = 0xFF
	data[10] = 0xFF
	data[11] = 0xFF

	// Set Name RVA
	data[12] = 0x00
	data[13] = 0x30
	data[14] = 0x00
	data[15] = 0x00

	// Set FirstThunk
	data[16] = 0x00
	data[17] = 0x40
	data[18] = 0x00
	data[19] = 0x00

	descriptor := &ImportDescriptor{}
	err := descriptor.Unpack(data, 0)

	if err != nil {
		t.Fatalf("Failed to unpack import descriptor: %v", err)
	}

	if descriptor.OriginalFirstThunk != 0x2000 {
		t.Errorf("Expected OriginalFirstThunk 0x2000, got 0x%08X", descriptor.OriginalFirstThunk)
	}

	if descriptor.Name != 0x3000 {
		t.Errorf("Expected Name 0x3000, got 0x%08X", descriptor.Name)
	}

	if descriptor.FirstThunk != 0x4000 {
		t.Errorf("Expected FirstThunk 0x4000, got 0x%08X", descriptor.FirstThunk)
	}
}

// TestThunkData32Unpack tests 32-bit thunk data unpacking
func TestThunkData32Unpack(t *testing.T) {
	// Create thunk data for import by name (ThunkData32 is actually just a union, so it's 4 bytes)
	data := make([]byte, 4)

	// Set address of data (not an ordinal)
	data[0] = 0x00
	data[1] = 0x50
	data[2] = 0x00
	data[3] = 0x00

	thunk := &ThunkData32{}
	err := thunk.Unpack(data, 0)

	if err != nil {
		t.Fatalf("Failed to unpack thunk data 32: %v", err)
	}

	if thunk.GetAddressOfData() != 0x5000 {
		t.Errorf("Expected AddressOfData 0x5000, got 0x%016X", thunk.GetAddressOfData())
	}

	// Test ordinal import
	data[0] = 0x01
	data[1] = 0x00
	data[2] = 0x00
	data[3] = 0x80 // Set ordinal flag

	thunk2 := &ThunkData32{}
	err = thunk2.Unpack(data, 0)

	if err != nil {
		t.Fatalf("Failed to unpack thunk data 32 (ordinal): %v", err)
	}

	if thunk2.GetOrdinal()&ImageOrdinalFlag == 0 {
		t.Error("Expected ordinal flag to be set")
	}
}

// TestExportDirectoryUnpack tests export directory unpacking
func TestExportDirectoryUnpack(t *testing.T) {
	// Create a minimal export directory
	data := make([]byte, 40)

	// Set characteristics (reserved, must be 0)
	data[0] = 0x00
	data[1] = 0x00
	data[2] = 0x00
	data[3] = 0x00

	// Set time/date stamp
	data[4] = 0x12
	data[5] = 0x34
	data[6] = 0x56
	data[7] = 0x78

	// Set major/minor version
	data[8] = 0x01
	data[9] = 0x00
	data[10] = 0x02
	data[11] = 0x00

	// Set name RVA
	data[12] = 0x00
	data[13] = 0x20
	data[14] = 0x00
	data[15] = 0x00

	// Set base ordinal
	data[16] = 0x01
	data[17] = 0x00
	data[18] = 0x00
	data[19] = 0x00

	// Set number of functions
	data[20] = 0x05
	data[21] = 0x00
	data[22] = 0x00
	data[23] = 0x00

	// Set number of names
	data[24] = 0x03
	data[25] = 0x00
	data[26] = 0x00
	data[27] = 0x00

	// Set address of functions
	data[28] = 0x00
	data[29] = 0x30
	data[30] = 0x00
	data[31] = 0x00

	// Set address of names
	data[32] = 0x00
	data[33] = 0x40
	data[34] = 0x00
	data[35] = 0x00

	// Set address of name ordinals
	data[36] = 0x00
	data[37] = 0x50
	data[38] = 0x00
	data[39] = 0x00

	exportDir := &ExportDirectory{}
	err := exportDir.Unpack(data, 0)

	if err != nil {
		t.Fatalf("Failed to unpack export directory: %v", err)
	}

	if exportDir.TimeDateStamp != 0x78563412 {
		t.Errorf("Expected TimeDateStamp 0x78563412, got 0x%08X", exportDir.TimeDateStamp)
	}

	if exportDir.MajorVersion != 1 {
		t.Errorf("Expected MajorVersion 1, got %d", exportDir.MajorVersion)
	}

	if exportDir.MinorVersion != 2 {
		t.Errorf("Expected MinorVersion 2, got %d", exportDir.MinorVersion)
	}

	if exportDir.Name != 0x2000 {
		t.Errorf("Expected Name 0x2000, got 0x%08X", exportDir.Name)
	}

	if exportDir.Base != 1 {
		t.Errorf("Expected Base 1, got %d", exportDir.Base)
	}

	if exportDir.NumberOfFunctions != 5 {
		t.Errorf("Expected NumberOfFunctions 5, got %d", exportDir.NumberOfFunctions)
	}

	if exportDir.NumberOfNames != 3 {
		t.Errorf("Expected NumberOfNames 3, got %d", exportDir.NumberOfNames)
	}

	if exportDir.AddressOfFunctions != 0x3000 {
		t.Errorf("Expected AddressOfFunctions 0x3000, got 0x%08X", exportDir.AddressOfFunctions)
	}

	if exportDir.AddressOfNames != 0x4000 {
		t.Errorf("Expected AddressOfNames 0x4000, got 0x%08X", exportDir.AddressOfNames)
	}

	if exportDir.AddressOfNameOrdinals != 0x5000 {
		t.Errorf("Expected AddressOfNameOrdinals 0x5000, got 0x%08X", exportDir.AddressOfNameOrdinals)
	}
}

// TestResourceDirectoryUnpack tests resource directory unpacking
func TestResourceDirectoryUnpack(t *testing.T) {
	// Create a minimal resource directory
	data := make([]byte, 16)

	// Set characteristics (reserved, must be 0)
	data[0] = 0x00
	data[1] = 0x00
	data[2] = 0x00
	data[3] = 0x00

	// Set time/date stamp
	data[4] = 0x12
	data[5] = 0x34
	data[6] = 0x56
	data[7] = 0x78

	// Set major/minor version
	data[8] = 0x01
	data[9] = 0x00
	data[10] = 0x02
	data[11] = 0x00

	// Set number of named entries
	data[12] = 0x01
	data[13] = 0x00

	// Set number of ID entries
	data[14] = 0x02
	data[15] = 0x00

	resourceDir := &ResourceDirectory{}
	err := resourceDir.Unpack(data, 0)

	if err != nil {
		t.Fatalf("Failed to unpack resource directory: %v", err)
	}

	if resourceDir.TimeDateStamp != 0x78563412 {
		t.Errorf("Expected TimeDateStamp 0x78563412, got 0x%08X", resourceDir.TimeDateStamp)
	}

	if resourceDir.MajorVersion != 1 {
		t.Errorf("Expected MajorVersion 1, got %d", resourceDir.MajorVersion)
	}

	if resourceDir.MinorVersion != 2 {
		t.Errorf("Expected MinorVersion 2, got %d", resourceDir.MinorVersion)
	}

	if resourceDir.NumberOfNamedEntries != 1 {
		t.Errorf("Expected NumberOfNamedEntries 1, got %d", resourceDir.NumberOfNamedEntries)
	}

	if resourceDir.NumberOfIdEntries != 2 {
		t.Errorf("Expected NumberOfIdEntries 2, got %d", resourceDir.NumberOfIdEntries)
	}
}

// TestResourceDirEntryUnpack tests resource directory entry unpacking
func TestResourceDirEntryUnpack(t *testing.T) {
	// Create a resource directory entry
	data := make([]byte, 8)

	// Set name/ID (ID entry)
	data[0] = 0x10
	data[1] = 0x00
	data[2] = 0x00
	data[3] = 0x00

	// Set offset to data (not a directory)
	data[4] = 0x00
	data[5] = 0x20
	data[6] = 0x00
	data[7] = 0x00

	entry := &ResourceDirEntry{}
	err := entry.Unpack(data, 0)

	if err != nil {
		t.Fatalf("Failed to unpack resource directory entry: %v", err)
	}

	if entry.Name != 0x10 {
		t.Errorf("Expected Name 0x10, got 0x%08X", entry.Name)
	}

	if entry.OffsetToData != 0x2000 {
		t.Errorf("Expected OffsetToData 0x2000, got 0x%08X", entry.OffsetToData)
	}

	if entry.IsNamedEntry() {
		t.Error("Expected ID entry, not named entry")
	}

	// Test named entry
	data[3] = 0x80 // Set high bit for named entry

	entry2 := &ResourceDirEntry{}
	err = entry2.Unpack(data, 0)

	if err != nil {
		t.Fatalf("Failed to unpack named resource directory entry: %v", err)
	}

	if !entry2.IsNamedEntry() {
		t.Error("Expected named entry")
	}
}

// TestResourceDataEntryUnpack tests resource data entry unpacking
func TestResourceDataEntryUnpack(t *testing.T) {
	// Create a resource data entry
	data := make([]byte, 16)

	// Set offset to data
	data[0] = 0x00
	data[1] = 0x30
	data[2] = 0x00
	data[3] = 0x00

	// Set size
	data[4] = 0x00
	data[5] = 0x10
	data[6] = 0x00
	data[7] = 0x00

	// Set code page
	data[8] = 0x00
	data[9] = 0x04
	data[10] = 0x00
	data[11] = 0x00

	// Set reserved
	data[12] = 0x00
	data[13] = 0x00
	data[14] = 0x00
	data[15] = 0x00

	dataEntry := &ResourceDataEntry{}
	err := dataEntry.Unpack(data, 0)

	if err != nil {
		t.Fatalf("Failed to unpack resource data entry: %v", err)
	}

	if dataEntry.OffsetToData != 0x3000 {
		t.Errorf("Expected OffsetToData 0x3000, got 0x%08X", dataEntry.OffsetToData)
	}

	if dataEntry.DataSize != 0x1000 {
		t.Errorf("Expected DataSize 0x1000, got 0x%08X", dataEntry.DataSize)
	}

	if dataEntry.CodePage != 0x400 {
		t.Errorf("Expected CodePage 0x400, got 0x%08X", dataEntry.CodePage)
	}
}

// TestHashCalculations tests hash calculation functions
func TestHashCalculations(t *testing.T) {
	// Create a minimal PE file data for testing
	data := make([]byte, 1024)

	// Set DOS signature
	data[0] = 0x4D // 'M'
	data[1] = 0x5A // 'Z'

	// Set e_lfanew to point to offset 0x80
	data[60] = 0x80
	data[61] = 0x00
	data[62] = 0x00
	data[63] = 0x00

	// Set NT signature at offset 0x80
	data[0x80] = 0x50 // 'P'
	data[0x81] = 0x45 // 'E'
	data[0x82] = 0x00
	data[0x83] = 0x00

	// Set machine type (i386)
	data[0x84] = 0x4C
	data[0x85] = 0x01

	// Set number of sections
	data[0x86] = 0x01
	data[0x87] = 0x00

	// Set size of optional header
	data[0x94] = 0xE0
	data[0x95] = 0x00

	// Set characteristics
	data[0x96] = 0x02
	data[0x97] = 0x00

	// Set optional header magic (PE32)
	data[0x98] = 0x0B
	data[0x99] = 0x01

	pe, err := NewPEFromData(data, "test.exe", false)
	if err != nil {
		t.Fatalf("Failed to create PE from data: %v", err)
	}
	defer pe.Close()

	// Test file hash calculations
	md5Hash := pe.CalculateFileHash(HashMD5)
	if md5Hash == "" {
		t.Error("MD5 hash should not be empty")
	}
	if len(md5Hash) != 32 {
		t.Errorf("MD5 hash should be 32 characters, got %d", len(md5Hash))
	}

	sha1Hash := pe.CalculateFileHash(HashSHA1)
	if sha1Hash == "" {
		t.Error("SHA1 hash should not be empty")
	}
	if len(sha1Hash) != 40 {
		t.Errorf("SHA1 hash should be 40 characters, got %d", len(sha1Hash))
	}

	sha256Hash := pe.CalculateFileHash(HashSHA256)
	if sha256Hash == "" {
		t.Error("SHA256 hash should not be empty")
	}
	if len(sha256Hash) != 64 {
		t.Errorf("SHA256 hash should be 64 characters, got %d", len(sha256Hash))
	}

	// Test import hash (should be empty for this test file)
	imphash := pe.CalculateImportHash()
	if imphash != "" {
		t.Error("Import hash should be empty for file with no imports")
	}

	// Test export hash (should be empty for this test file)
	exphash := pe.CalculateExportHash()
	if exphash != "" {
		t.Error("Export hash should be empty for file with no exports")
	}

	// Test PE checksum calculation
	checksum := pe.CalculatePEChecksum()
	if checksum == 0 {
		t.Error("PE checksum should not be zero")
	}

	// Test hash summary
	summary := pe.GetHashSummary()
	if len(summary) == 0 {
		t.Error("Hash summary should not be empty")
	}

	// Check that required hashes are present
	requiredHashes := []string{"MD5", "SHA1", "SHA256", "PE_Checksum", "Checksum_Valid"}
	for _, hashName := range requiredHashes {
		if _, exists := summary[hashName]; !exists {
			t.Errorf("Hash summary should contain %s", hashName)
		}
	}
}

// TestUtilityFunctionsExtended tests additional utility functions
func TestUtilityFunctionsExtended(t *testing.T) {
	// Test TrimNullBytes
	data := []byte{'H', 'e', 'l', 'l', 'o', 0, 0, 0}
	trimmed := TrimNullBytes(data)
	expected := []byte{'H', 'e', 'l', 'l', 'o'}
	if string(trimmed) != string(expected) {
		t.Errorf("Expected %v, got %v", expected, trimmed)
	}

	// Test CalculateEntropy
	uniformData := make([]byte, 256)
	for i := 0; i < 256; i++ {
		uniformData[i] = byte(i)
	}
	entropy := CalculateEntropy(uniformData)
	if entropy < 7.9 || entropy > 8.1 { // Should be close to 8 for uniform distribution
		t.Errorf("Expected entropy around 8.0, got %.2f", entropy)
	}

	// Test low entropy data
	lowEntropyData := make([]byte, 1000)
	for i := range lowEntropyData {
		lowEntropyData[i] = 0x41 // All 'A'
	}
	lowEntropy := CalculateEntropy(lowEntropyData)
	if lowEntropy > 0.1 {
		t.Errorf("Expected low entropy, got %.2f", lowEntropy)
	}

	// Test IsPrintableASCII
	printable := []byte("Hello World!")
	if !IsPrintableASCII(printable) {
		t.Error("Expected printable ASCII to be detected")
	}

	nonPrintable := []byte{'H', 'e', 'l', 'l', 'o', 0x01, 'W', 'o', 'r', 'l', 'd'}
	if IsPrintableASCII(nonPrintable) {
		t.Error("Expected non-printable ASCII to be detected")
	}

	// Test IsValidIdentifier
	validIds := []string{"hello", "Hello", "_test", "test123", "_123"}
	for _, id := range validIds {
		if !IsValidIdentifier(id) {
			t.Errorf("Expected '%s' to be a valid identifier", id)
		}
	}

	invalidIds := []string{"", "123", "hello-world", "hello world", "hello.world"}
	for _, id := range invalidIds {
		if IsValidIdentifier(id) {
			t.Errorf("Expected '%s' to be an invalid identifier", id)
		}
	}

	// Test FormatHex
	tests := []struct {
		value    interface{}
		expected string
	}{
		{uint8(255), "0xFF"},
		{uint16(4096), "0x1000"},
		{uint32(65536), "0x00010000"},
		{uint64(4294967296), "0x0000000100000000"},
	}

	for _, test := range tests {
		result := FormatHex(test.value)
		if result != test.expected {
			t.Errorf("FormatHex(%v): expected %s, got %s", test.value, test.expected, result)
		}
	}

	// Test IsValidPESignature
	validPE := make([]byte, 100)
	validPE[0] = 0x4D    // 'M'
	validPE[1] = 0x5A    // 'Z'
	validPE[60] = 0x50   // e_lfanew = 0x50
	validPE[0x50] = 0x50 // 'P'
	validPE[0x51] = 0x45 // 'E'
	validPE[0x52] = 0x00
	validPE[0x53] = 0x00

	if !IsValidPESignature(validPE) {
		t.Error("Expected valid PE signature to be detected")
	}

	invalidPE := make([]byte, 100)
	invalidPE[0] = 0x7F // Invalid DOS signature
	invalidPE[1] = 0x45

	if IsValidPESignature(invalidPE) {
		t.Error("Expected invalid PE signature to be detected")
	}
}

// TestOrdinalLookup tests ordinal lookup functionality
func TestOrdinalLookup(t *testing.T) {
	// Test known ordinals
	testCases := []struct {
		dll      string
		ordinal  uint16
		expected string
	}{
		{"oleaut32.dll", 2, "SysAllocString"},
		{"OLEAUT32.DLL", 2, "SysAllocString"}, // Case insensitive
		{"ws2_32.dll", 1, "accept"},
		{"wsock32.dll", 1, "accept"},
		{"oleaut32.dll", 999, "ord999"}, // Unknown ordinal
		{"unknown.dll", 1, ""},          // Unknown DLL, makeName=false
	}

	for _, tc := range testCases {
		result := OrdLookup(tc.dll, tc.ordinal, false)
		if result != tc.expected {
			t.Errorf("OrdLookup(%s, %d, false): expected %s, got %s",
				tc.dll, tc.ordinal, tc.expected, result)
		}
	}

	// Test makeName=true for unknown DLL
	result := OrdLookup("unknown.dll", 123, true)
	expected := "ord123"
	if result != expected {
		t.Errorf("OrdLookup(unknown.dll, 123, true): expected %s, got %s", expected, result)
	}

	// Test FormatOrdString
	ordStr := FormatOrdString(456)
	expectedOrdStr := "ord456"
	if ordStr != expectedOrdStr {
		t.Errorf("FormatOrdString(456): expected %s, got %s", expectedOrdStr, ordStr)
	}

	// Test GetSupportedDLLs
	supportedDLLs := GetSupportedDLLs()
	if len(supportedDLLs) == 0 {
		t.Error("Expected at least one supported DLL")
	}

	// Check that known DLLs are supported
	knownDLLs := []string{"oleaut32.dll", "ws2_32.dll", "wsock32.dll"}
	for _, dll := range knownDLLs {
		if !HasOrdinalMapping(dll) {
			t.Errorf("Expected %s to have ordinal mapping", dll)
		}
	}

	// Test GetOrdinalCount
	count := GetOrdinalCount("oleaut32.dll")
	if count == 0 {
		t.Error("Expected oleaut32.dll to have ordinal mappings")
	}

	unknownCount := GetOrdinalCount("unknown.dll")
	if unknownCount != 0 {
		t.Error("Expected unknown.dll to have no ordinal mappings")
	}

	// Test AddOrdinalMapping
	testMappings := map[uint16]string{
		1: "TestFunction1",
		2: "TestFunction2",
	}
	AddOrdinalMapping("test.dll", testMappings)

	result = OrdLookup("test.dll", 1, false)
	if result != "TestFunction1" {
		t.Errorf("Expected TestFunction1, got %s", result)
	}

	if !HasOrdinalMapping("test.dll") {
		t.Error("Expected test.dll to have ordinal mapping after adding")
	}
}

// TestValidation tests PE validation functionality
func TestValidation(t *testing.T) {
	// Create a minimal valid PE file data for testing
	data := make([]byte, 1024)

	// Set DOS signature
	data[0] = 0x4D // 'M'
	data[1] = 0x5A // 'Z'

	// Set e_lfanew to point to offset 0x80
	data[60] = 0x80
	data[61] = 0x00
	data[62] = 0x00
	data[63] = 0x00

	// Set NT signature at offset 0x80
	data[0x80] = 0x50 // 'P'
	data[0x81] = 0x45 // 'E'
	data[0x82] = 0x00
	data[0x83] = 0x00

	// Set machine type (i386)
	data[0x84] = 0x4C
	data[0x85] = 0x01

	// Set number of sections
	data[0x86] = 0x01
	data[0x87] = 0x00

	// Set size of optional header
	data[0x94] = 0xE0
	data[0x95] = 0x00

	// Set characteristics
	data[0x96] = 0x02
	data[0x97] = 0x00

	// Set optional header magic (PE32)
	data[0x98] = 0x0B
	data[0x99] = 0x01

	pe, err := NewPEFromData(data, "test.exe", false)
	if err != nil {
		t.Fatalf("Failed to create PE from data: %v", err)
	}
	defer pe.Close()

	// Test validation
	result := pe.ValidatePE()
	if result == nil {
		t.Fatal("Validation result should not be nil")
	}

	// Should have some warnings due to minimal structure
	if !result.HasIssues() {
		t.Error("Expected validation to find some issues with minimal PE structure")
	}

	// Test ValidationResult methods
	result.AddError("Test error")
	result.AddWarning("Test warning")

	if result.IsValid {
		t.Error("Expected IsValid to be false after adding error")
	}

	if !result.HasIssues() {
		t.Error("Expected HasIssues to be true after adding error and warning")
	}

	if len(result.Errors) == 0 {
		t.Error("Expected at least one error")
	}

	if len(result.Warnings) == 0 {
		t.Error("Expected at least one warning")
	}
}

// TestWarningListEnhancements tests enhanced warning functionality
func TestWarningListEnhancements(t *testing.T) {
	var warnings WarningList

	// Test basic functionality
	warnings.Add("Test warning 1", 0x100)
	warnings.AddString("Test warning 2")

	if !warnings.HasWarnings() {
		t.Error("Expected HasWarnings to be true")
	}

	if warnings.Len() != 2 {
		t.Errorf("Expected 2 warnings, got %d", warnings.Len())
	}

	// Test AddWithContext
	context := map[string]interface{}{
		"section": "test",
		"index":   1,
	}
	warnings.AddWithContext("Context warning", 0x200, context)

	if warnings.Len() != 3 {
		t.Errorf("Expected 3 warnings, got %d", warnings.Len())
	}

	// Test AddError
	testErr := fmt.Errorf("test error")
	warnings.AddError(testErr, 0x300)

	if warnings.Len() != 4 {
		t.Errorf("Expected 4 warnings, got %d", warnings.Len())
	}

	// Test GetByOffset
	offsetWarnings := warnings.GetByOffset(0x100)
	if len(offsetWarnings) != 1 {
		t.Errorf("Expected 1 warning at offset 0x100, got %d", len(offsetWarnings))
	}

	// Test Filter
	filteredWarnings := warnings.Filter(func(w Warning) bool {
		return w.Offset > 0
	})
	if len(filteredWarnings) != 3 { // All except the string-only warning
		t.Errorf("Expected 3 filtered warnings, got %d", len(filteredWarnings))
	}

	// Test Contains
	if !warnings.Contains("Test warning 1") {
		t.Error("Expected to find 'Test warning 1'")
	}

	if warnings.Contains("Non-existent warning") {
		t.Error("Should not find non-existent warning")
	}

	// Test GetSummary
	summary := warnings.GetSummary()
	if len(summary) == 0 {
		t.Error("Expected non-empty summary")
	}

	// Test Clear
	warnings.Clear()
	if warnings.HasWarnings() {
		t.Error("Expected no warnings after clear")
	}

	if warnings.Len() != 0 {
		t.Errorf("Expected 0 warnings after clear, got %d", warnings.Len())
	}
}
