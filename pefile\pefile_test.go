package pefile

import (
	"testing"
)

// TestDOSHeaderUnpack tests DOS header unpacking
func TestDOSHeaderUnpack(t *testing.T) {
	// Create a minimal DOS header
	data := make([]byte, 64)

	// Set DOS signature "MZ"
	data[0] = 0x4D // 'M'
	data[1] = 0x5A // 'Z'

	// Set e_lfanew to point to offset 0x80
	data[60] = 0x80
	data[61] = 0x00
	data[62] = 0x00
	data[63] = 0x00

	header := &DOSHeader{}
	err := header.Unpack(data, 0)

	if err != nil {
		t.Fatalf("Failed to unpack DOS header: %v", err)
	}

	if header.Magic != ImageDOSSignature {
		t.Errorf("Expected DOS signature 0x%04X, got 0x%04X", ImageDOSSignature, header.Magic)
	}

	if header.NewHeaderAddr != 0x80 {
		t.Errorf("Expected e_lfanew 0x80, got 0x%08X", header.NewHeaderAddr)
	}
}

// TestFileHeaderUnpack tests file header unpacking
func TestFileHeaderUnpack(t *testing.T) {
	// Create a minimal file header
	data := make([]byte, 20)

	// Set machine type to i386
	data[0] = 0x4C
	data[1] = 0x01

	// Set number of sections to 3
	data[2] = 0x03
	data[3] = 0x00

	// Set characteristics to executable image
	data[18] = 0x02
	data[19] = 0x00

	header := &FileHeader{}
	err := header.Unpack(data, 0)

	if err != nil {
		t.Fatalf("Failed to unpack file header: %v", err)
	}

	if header.Machine != MachineI386 {
		t.Errorf("Expected machine type 0x%04X, got 0x%04X", MachineI386, header.Machine)
	}

	if header.NumberOfSections != 3 {
		t.Errorf("Expected 3 sections, got %d", header.NumberOfSections)
	}

	if header.Characteristics&ImageFileExecutableImage == 0 {
		t.Error("Expected executable image characteristic to be set")
	}
}

// TestOptionalHeader32Unpack tests 32-bit optional header unpacking
func TestOptionalHeader32Unpack(t *testing.T) {
	// Create a minimal 32-bit optional header
	data := make([]byte, 224)

	// Set magic to PE32
	data[0] = 0x0B
	data[1] = 0x01

	// Set entry point
	data[16] = 0x00
	data[17] = 0x10
	data[18] = 0x00
	data[19] = 0x00

	// Set image base
	data[28] = 0x00
	data[29] = 0x00
	data[30] = 0x40
	data[31] = 0x00

	// Set subsystem to Windows GUI
	data[68] = 0x02
	data[69] = 0x00

	header := &OptionalHeader32{}
	err := header.Unpack(data, 0)

	if err != nil {
		t.Fatalf("Failed to unpack optional header 32: %v", err)
	}

	if header.Magic != OptionalHeaderMagicPE {
		t.Errorf("Expected magic 0x%04X, got 0x%04X", OptionalHeaderMagicPE, header.Magic)
	}

	if header.AddressOfEntryPoint != 0x1000 {
		t.Errorf("Expected entry point 0x1000, got 0x%08X", header.AddressOfEntryPoint)
	}

	if header.ImageBase != 0x400000 {
		t.Errorf("Expected image base 0x400000, got 0x%08X", header.ImageBase)
	}

	if header.Subsystem != SubsystemWindowsGUI {
		t.Errorf("Expected subsystem %d, got %d", SubsystemWindowsGUI, header.Subsystem)
	}
}

// TestSectionUnpack tests section header unpacking
func TestSectionUnpack(t *testing.T) {
	// Create a minimal section header
	data := make([]byte, 40)

	// Set section name ".text"
	copy(data[0:8], []byte(".text\x00\x00\x00"))

	// Set virtual size
	data[8] = 0x00
	data[9] = 0x10
	data[10] = 0x00
	data[11] = 0x00

	// Set virtual address
	data[12] = 0x00
	data[13] = 0x10
	data[14] = 0x00
	data[15] = 0x00

	// Set size of raw data
	data[16] = 0x00
	data[17] = 0x10
	data[18] = 0x00
	data[19] = 0x00

	// Set pointer to raw data
	data[20] = 0x00
	data[21] = 0x04
	data[22] = 0x00
	data[23] = 0x00

	// Set characteristics (code + execute + read)
	// SectionCntCode (0x20) + SectionMemExecute (0x20000000) + SectionMemRead (0x40000000) = 0x60000020
	data[36] = 0x20
	data[37] = 0x00
	data[38] = 0x00
	data[39] = 0x60

	section := &Section{}
	err := section.UnpackWithPE(data, 0, nil)

	if err != nil {
		t.Fatalf("Failed to unpack section: %v", err)
	}

	if section.GetName() != ".text" {
		t.Errorf("Expected section name '.text', got '%s'", section.GetName())
	}

	if section.VirtualSize != 0x1000 {
		t.Errorf("Expected virtual size 0x1000, got 0x%08X", section.VirtualSize)
	}

	if section.VirtualAddress != 0x1000 {
		t.Errorf("Expected virtual address 0x1000, got 0x%08X", section.VirtualAddress)
	}

	if !section.ContainsCode() {
		t.Error("Expected section to contain code")
	}

	if !section.IsExecutable() {
		t.Error("Expected section to be executable")
	}

	if !section.IsReadable() {
		t.Error("Expected section to be readable")
	}
}

// TestConstants tests that constants are defined correctly
func TestConstants(t *testing.T) {
	if ImageDOSSignature != 0x5A4D {
		t.Errorf("Expected DOS signature 0x5A4D, got 0x%04X", ImageDOSSignature)
	}

	if ImageNTSignature != 0x00004550 {
		t.Errorf("Expected NT signature 0x00004550, got 0x%08X", ImageNTSignature)
	}

	if OptionalHeaderMagicPE != 0x10B {
		t.Errorf("Expected PE32 magic 0x10B, got 0x%04X", OptionalHeaderMagicPE)
	}

	if OptionalHeaderMagicPEPlus != 0x20B {
		t.Errorf("Expected PE32+ magic 0x20B, got 0x%04X", OptionalHeaderMagicPEPlus)
	}
}

// TestUtilityFunctions tests utility functions
func TestUtilityFunctions(t *testing.T) {
	// Test BytesToString
	data := []byte{'H', 'e', 'l', 'l', 'o', 0, 'W', 'o', 'r', 'l', 'd'}
	str := BytesToString(data)
	if str != "Hello" {
		t.Errorf("Expected 'Hello', got '%s'", str)
	}

	// Test AlignUp
	aligned := AlignUp(0x123, 0x1000)
	if aligned != 0x1000 {
		t.Errorf("Expected 0x1000, got 0x%08X", aligned)
	}

	aligned = AlignUp(0x1000, 0x1000)
	if aligned != 0x1000 {
		t.Errorf("Expected 0x1000, got 0x%08X", aligned)
	}

	aligned = AlignUp(0x1001, 0x1000)
	if aligned != 0x2000 {
		t.Errorf("Expected 0x2000, got 0x%08X", aligned)
	}

	// Test AlignDown
	aligned = AlignDown(0x1234, 0x1000)
	if aligned != 0x1000 {
		t.Errorf("Expected 0x1000, got 0x%08X", aligned)
	}

	aligned = AlignDown(0x1000, 0x1000)
	if aligned != 0x1000 {
		t.Errorf("Expected 0x1000, got 0x%08X", aligned)
	}

	// Test IsAllZeroes
	zeros := make([]byte, 10)
	if !IsAllZeroes(zeros) {
		t.Error("Expected all zeroes to be detected")
	}

	zeros[5] = 1
	if IsAllZeroes(zeros) {
		t.Error("Expected non-zero byte to be detected")
	}
}

// TestImportDescriptorUnpack tests import descriptor unpacking
func TestImportDescriptorUnpack(t *testing.T) {
	// Create a minimal import descriptor
	data := make([]byte, 20)

	// Set OriginalFirstThunk
	data[0] = 0x00
	data[1] = 0x20
	data[2] = 0x00
	data[3] = 0x00

	// Set TimeDateStamp
	data[4] = 0x00
	data[5] = 0x00
	data[6] = 0x00
	data[7] = 0x00

	// Set ForwarderChain
	data[8] = 0xFF
	data[9] = 0xFF
	data[10] = 0xFF
	data[11] = 0xFF

	// Set Name RVA
	data[12] = 0x00
	data[13] = 0x30
	data[14] = 0x00
	data[15] = 0x00

	// Set FirstThunk
	data[16] = 0x00
	data[17] = 0x40
	data[18] = 0x00
	data[19] = 0x00

	descriptor := &ImportDescriptor{}
	err := descriptor.Unpack(data, 0)

	if err != nil {
		t.Fatalf("Failed to unpack import descriptor: %v", err)
	}

	if descriptor.OriginalFirstThunk != 0x2000 {
		t.Errorf("Expected OriginalFirstThunk 0x2000, got 0x%08X", descriptor.OriginalFirstThunk)
	}

	if descriptor.Name != 0x3000 {
		t.Errorf("Expected Name 0x3000, got 0x%08X", descriptor.Name)
	}

	if descriptor.FirstThunk != 0x4000 {
		t.Errorf("Expected FirstThunk 0x4000, got 0x%08X", descriptor.FirstThunk)
	}
}

// TestThunkData32Unpack tests 32-bit thunk data unpacking
func TestThunkData32Unpack(t *testing.T) {
	// Create thunk data for import by name (ThunkData32 is actually just a union, so it's 4 bytes)
	data := make([]byte, 4)

	// Set address of data (not an ordinal)
	data[0] = 0x00
	data[1] = 0x50
	data[2] = 0x00
	data[3] = 0x00

	thunk := &ThunkData32{}
	err := thunk.Unpack(data, 0)

	if err != nil {
		t.Fatalf("Failed to unpack thunk data 32: %v", err)
	}

	if thunk.GetAddressOfData() != 0x5000 {
		t.Errorf("Expected AddressOfData 0x5000, got 0x%016X", thunk.GetAddressOfData())
	}

	// Test ordinal import
	data[0] = 0x01
	data[1] = 0x00
	data[2] = 0x00
	data[3] = 0x80 // Set ordinal flag

	thunk2 := &ThunkData32{}
	err = thunk2.Unpack(data, 0)

	if err != nil {
		t.Fatalf("Failed to unpack thunk data 32 (ordinal): %v", err)
	}

	if thunk2.GetOrdinal()&ImageOrdinalFlag == 0 {
		t.Error("Expected ordinal flag to be set")
	}
}

// TestExportDirectoryUnpack tests export directory unpacking
func TestExportDirectoryUnpack(t *testing.T) {
	// Create a minimal export directory
	data := make([]byte, 40)

	// Set characteristics (reserved, must be 0)
	data[0] = 0x00
	data[1] = 0x00
	data[2] = 0x00
	data[3] = 0x00

	// Set time/date stamp
	data[4] = 0x12
	data[5] = 0x34
	data[6] = 0x56
	data[7] = 0x78

	// Set major/minor version
	data[8] = 0x01
	data[9] = 0x00
	data[10] = 0x02
	data[11] = 0x00

	// Set name RVA
	data[12] = 0x00
	data[13] = 0x20
	data[14] = 0x00
	data[15] = 0x00

	// Set base ordinal
	data[16] = 0x01
	data[17] = 0x00
	data[18] = 0x00
	data[19] = 0x00

	// Set number of functions
	data[20] = 0x05
	data[21] = 0x00
	data[22] = 0x00
	data[23] = 0x00

	// Set number of names
	data[24] = 0x03
	data[25] = 0x00
	data[26] = 0x00
	data[27] = 0x00

	// Set address of functions
	data[28] = 0x00
	data[29] = 0x30
	data[30] = 0x00
	data[31] = 0x00

	// Set address of names
	data[32] = 0x00
	data[33] = 0x40
	data[34] = 0x00
	data[35] = 0x00

	// Set address of name ordinals
	data[36] = 0x00
	data[37] = 0x50
	data[38] = 0x00
	data[39] = 0x00

	exportDir := &ExportDirectory{}
	err := exportDir.Unpack(data, 0)

	if err != nil {
		t.Fatalf("Failed to unpack export directory: %v", err)
	}

	if exportDir.TimeDateStamp != 0x78563412 {
		t.Errorf("Expected TimeDateStamp 0x78563412, got 0x%08X", exportDir.TimeDateStamp)
	}

	if exportDir.MajorVersion != 1 {
		t.Errorf("Expected MajorVersion 1, got %d", exportDir.MajorVersion)
	}

	if exportDir.MinorVersion != 2 {
		t.Errorf("Expected MinorVersion 2, got %d", exportDir.MinorVersion)
	}

	if exportDir.Name != 0x2000 {
		t.Errorf("Expected Name 0x2000, got 0x%08X", exportDir.Name)
	}

	if exportDir.Base != 1 {
		t.Errorf("Expected Base 1, got %d", exportDir.Base)
	}

	if exportDir.NumberOfFunctions != 5 {
		t.Errorf("Expected NumberOfFunctions 5, got %d", exportDir.NumberOfFunctions)
	}

	if exportDir.NumberOfNames != 3 {
		t.Errorf("Expected NumberOfNames 3, got %d", exportDir.NumberOfNames)
	}

	if exportDir.AddressOfFunctions != 0x3000 {
		t.Errorf("Expected AddressOfFunctions 0x3000, got 0x%08X", exportDir.AddressOfFunctions)
	}

	if exportDir.AddressOfNames != 0x4000 {
		t.Errorf("Expected AddressOfNames 0x4000, got 0x%08X", exportDir.AddressOfNames)
	}

	if exportDir.AddressOfNameOrdinals != 0x5000 {
		t.Errorf("Expected AddressOfNameOrdinals 0x5000, got 0x%08X", exportDir.AddressOfNameOrdinals)
	}
}
