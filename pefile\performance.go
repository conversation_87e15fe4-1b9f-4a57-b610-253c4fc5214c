package pefile

import (
	"sync"
)

// Cache for frequently accessed data
type PECache struct {
	mu                sync.RWMutex
	rvaToOffsetCache  map[uint32]uint32
	sectionDataCache  map[int][]byte
	stringCache       map[uint32]string
	hashCache         map[string]string
	validationCache   *ValidationResult
	cacheEnabled      bool
}

// NewPECache creates a new PE cache
func NewPECache() *PECache {
	return &PECache{
		rvaToOffsetCache: make(map[uint32]uint32),
		sectionDataCache: make(map[int][]byte),
		stringCache:      make(map[uint32]string),
		hashCache:        make(map[string]string),
		cacheEnabled:     true,
	}
}

// EnableCache enables or disables caching
func (cache *PECache) EnableCache(enabled bool) {
	cache.mu.Lock()
	defer cache.mu.Unlock()
	cache.cacheEnabled = enabled
}

// ClearCache clears all cached data
func (cache *PECache) ClearCache() {
	cache.mu.Lock()
	defer cache.mu.Unlock()
	
	cache.rvaToOffsetCache = make(map[uint32]uint32)
	cache.sectionDataCache = make(map[int][]byte)
	cache.stringCache = make(map[uint32]string)
	cache.hashCache = make(map[string]string)
	cache.validationCache = nil
}

// GetRVAToOffset gets cached RVA to offset mapping
func (cache *PECache) GetRVAToOffset(rva uint32) (uint32, bool) {
	if !cache.cacheEnabled {
		return 0, false
	}
	
	cache.mu.RLock()
	defer cache.mu.RUnlock()
	
	offset, exists := cache.rvaToOffsetCache[rva]
	return offset, exists
}

// SetRVAToOffset sets cached RVA to offset mapping
func (cache *PECache) SetRVAToOffset(rva, offset uint32) {
	if !cache.cacheEnabled {
		return
	}
	
	cache.mu.Lock()
	defer cache.mu.Unlock()
	
	cache.rvaToOffsetCache[rva] = offset
}

// GetSectionData gets cached section data
func (cache *PECache) GetSectionData(index int) ([]byte, bool) {
	if !cache.cacheEnabled {
		return nil, false
	}
	
	cache.mu.RLock()
	defer cache.mu.RUnlock()
	
	data, exists := cache.sectionDataCache[index]
	return data, exists
}

// SetSectionData sets cached section data
func (cache *PECache) SetSectionData(index int, data []byte) {
	if !cache.cacheEnabled {
		return
	}
	
	cache.mu.Lock()
	defer cache.mu.Unlock()
	
	// Make a copy to avoid data races
	dataCopy := make([]byte, len(data))
	copy(dataCopy, data)
	cache.sectionDataCache[index] = dataCopy
}

// GetString gets cached string
func (cache *PECache) GetString(offset uint32) (string, bool) {
	if !cache.cacheEnabled {
		return "", false
	}
	
	cache.mu.RLock()
	defer cache.mu.RUnlock()
	
	str, exists := cache.stringCache[offset]
	return str, exists
}

// SetString sets cached string
func (cache *PECache) SetString(offset uint32, str string) {
	if !cache.cacheEnabled {
		return
	}
	
	cache.mu.Lock()
	defer cache.mu.Unlock()
	
	cache.stringCache[offset] = str
}

// GetHash gets cached hash
func (cache *PECache) GetHash(hashType string) (string, bool) {
	if !cache.cacheEnabled {
		return "", false
	}
	
	cache.mu.RLock()
	defer cache.mu.RUnlock()
	
	hash, exists := cache.hashCache[hashType]
	return hash, exists
}

// SetHash sets cached hash
func (cache *PECache) SetHash(hashType, hash string) {
	if !cache.cacheEnabled {
		return
	}
	
	cache.mu.Lock()
	defer cache.mu.Unlock()
	
	cache.hashCache[hashType] = hash
}

// GetValidation gets cached validation result
func (cache *PECache) GetValidation() (*ValidationResult, bool) {
	if !cache.cacheEnabled {
		return nil, false
	}
	
	cache.mu.RLock()
	defer cache.mu.RUnlock()
	
	return cache.validationCache, cache.validationCache != nil
}

// SetValidation sets cached validation result
func (cache *PECache) SetValidation(result *ValidationResult) {
	if !cache.cacheEnabled {
		return
	}
	
	cache.mu.Lock()
	defer cache.mu.Unlock()
	
	cache.validationCache = result
}

// GetCacheStats returns cache statistics
func (cache *PECache) GetCacheStats() map[string]int {
	cache.mu.RLock()
	defer cache.mu.RUnlock()
	
	return map[string]int{
		"rva_to_offset_entries": len(cache.rvaToOffsetCache),
		"section_data_entries":  len(cache.sectionDataCache),
		"string_entries":        len(cache.stringCache),
		"hash_entries":          len(cache.hashCache),
		"validation_cached":     boolToInt(cache.validationCache != nil),
		"cache_enabled":         boolToInt(cache.cacheEnabled),
	}
}

// boolToInt converts bool to int for stats
func boolToInt(b bool) int {
	if b {
		return 1
	}
	return 0
}

// OptimizedGetOffsetFromRVA is an optimized version of GetOffsetFromRVA with caching
func (pe *PE) OptimizedGetOffsetFromRVA(rva uint32) (uint32, error) {
	// Check cache first
	if pe.cache != nil {
		if offset, exists := pe.cache.GetRVAToOffset(rva); exists {
			return offset, nil
		}
	}
	
	// Calculate offset
	offset, err := pe.GetOffsetFromRVA(rva)
	if err != nil {
		return 0, err
	}
	
	// Cache the result
	if pe.cache != nil {
		pe.cache.SetRVAToOffset(rva, offset)
	}
	
	return offset, nil
}

// OptimizedCalculateFileHash calculates file hash with caching
func (pe *PE) OptimizedCalculateFileHash(hashType HashType) string {
	hashTypeStr := ""
	switch hashType {
	case HashMD5:
		hashTypeStr = "md5"
	case HashSHA1:
		hashTypeStr = "sha1"
	case HashSHA256:
		hashTypeStr = "sha256"
	default:
		return ""
	}
	
	// Check cache first
	if pe.cache != nil {
		if hash, exists := pe.cache.GetHash(hashTypeStr); exists {
			return hash
		}
	}
	
	// Calculate hash
	hash := pe.CalculateFileHash(hashType)
	
	// Cache the result
	if pe.cache != nil {
		pe.cache.SetHash(hashTypeStr, hash)
	}
	
	return hash
}

// OptimizedValidatePE performs validation with caching
func (pe *PE) OptimizedValidatePE() *ValidationResult {
	// Check cache first
	if pe.cache != nil {
		if result, exists := pe.cache.GetValidation(); exists {
			return result
		}
	}
	
	// Perform validation
	result := pe.ValidatePE()
	
	// Cache the result
	if pe.cache != nil {
		pe.cache.SetValidation(result)
	}
	
	return result
}

// EnablePerformanceOptimizations enables performance optimizations
func (pe *PE) EnablePerformanceOptimizations() {
	if pe.cache == nil {
		pe.cache = NewPECache()
	}
	pe.cache.EnableCache(true)
}

// DisablePerformanceOptimizations disables performance optimizations
func (pe *PE) DisablePerformanceOptimizations() {
	if pe.cache != nil {
		pe.cache.EnableCache(false)
	}
}

// GetPerformanceStats returns performance statistics
func (pe *PE) GetPerformanceStats() map[string]interface{} {
	stats := make(map[string]interface{})
	
	if pe.cache != nil {
		stats["cache"] = pe.cache.GetCacheStats()
	} else {
		stats["cache"] = "disabled"
	}
	
	stats["file_size"] = len(pe.data)
	stats["sections_count"] = len(pe.Sections)
	stats["imports_count"] = len(pe.Imports)
	
	if pe.ExportDirectory != nil {
		stats["exports_count"] = len(pe.ExportDirectory.Exports)
	} else {
		stats["exports_count"] = 0
	}
	
	stats["warnings_count"] = pe.warnings.Len()
	
	return stats
}

// PreloadCriticalData preloads frequently accessed data into cache
func (pe *PE) PreloadCriticalData() {
	if pe.cache == nil {
		return
	}
	
	// Preload section data
	for i, section := range pe.Sections {
		if section != nil {
			if data, err := section.GetData(); err == nil {
				pe.cache.SetSectionData(i, data)
			}
		}
	}
	
	// Preload common RVA mappings
	commonRVAs := []uint32{0x1000, 0x2000, 0x3000, 0x4000, 0x5000}
	for _, rva := range commonRVAs {
		if offset, err := pe.GetOffsetFromRVA(rva); err == nil {
			pe.cache.SetRVAToOffset(rva, offset)
		}
	}
}

// MemoryPool for reusing byte slices
type MemoryPool struct {
	pool sync.Pool
}

// NewMemoryPool creates a new memory pool
func NewMemoryPool() *MemoryPool {
	return &MemoryPool{
		pool: sync.Pool{
			New: func() interface{} {
				return make([]byte, 0, 1024) // Start with 1KB capacity
			},
		},
	}
}

// Get gets a byte slice from the pool
func (mp *MemoryPool) Get(size int) []byte {
	buf := mp.pool.Get().([]byte)
	if cap(buf) < size {
		// If capacity is too small, create a new slice
		return make([]byte, size)
	}
	return buf[:size]
}

// Put returns a byte slice to the pool
func (mp *MemoryPool) Put(buf []byte) {
	if cap(buf) <= 64*1024 { // Don't pool very large buffers
		buf = buf[:0] // Reset length but keep capacity
		mp.pool.Put(buf)
	}
}

// Global memory pool instance
var globalMemoryPool = NewMemoryPool()

// GetPooledBuffer gets a buffer from the global memory pool
func GetPooledBuffer(size int) []byte {
	return globalMemoryPool.Get(size)
}

// PutPooledBuffer returns a buffer to the global memory pool
func PutPooledBuffer(buf []byte) {
	globalMemoryPool.Put(buf)
}
