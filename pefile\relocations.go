package pefile

import (
	"fmt"
)

// BaseRelocation represents the IMAGE_BASE_RELOCATION structure
type BaseRelocation struct {
	BaseStructure
	VirtualAddress uint32 // RVA of the relocation block
	SizeOfBlock    uint32 // Size of the relocation block

	// Parsed data
	Entries []*RelocationEntry // Relocation entries
}

// RelocationEntry represents a single relocation entry
type RelocationEntry struct {
	Type   uint16 // Relocation type
	Offset uint16 // Offset within the page
	RVA    uint32 // Calculated RVA (VirtualAddress + Offset)
}

// Relocation types
const (
	RelocAbsolute      = 0  // IMAGE_REL_BASED_ABSOLUTE
	RelocHigh          = 1  // IMAGE_REL_BASED_HIGH
	RelocLow           = 2  // IMAGE_REL_BASED_LOW
	RelocHighLow       = 3  // IMAGE_REL_BASED_HIGHLOW
	RelocHighAdj       = 4  // IMAGE_REL_BASED_HIGHADJ
	RelocMIPSJmpAddr   = 5  // IMAGE_REL_BASED_MIPS_JMPADDR
	RelocARMMovT       = 5  // IMAGE_REL_BASED_ARM_MOV32T
	RelocRISCVHigh20   = 5  // IMAGE_REL_BASED_RISCV_HIGH20
	RelocThumbMovT     = 7  // IMAGE_REL_BASED_THUMB_MOV32
	RelocRISCVLow12I   = 7  // IMAGE_REL_BASED_RISCV_LOW12I
	RelocRISCVLow12S   = 8  // IMAGE_REL_BASED_RISCV_LOW12S
	RelocMIPSJmpAddr16 = 9  // IMAGE_REL_BASED_MIPS_JMPADDR16
	RelocDir64         = 10 // IMAGE_REL_BASED_DIR64
)

// RelocationTypeNames maps relocation types to their string names
var RelocationTypeNames = map[uint16]string{
	RelocAbsolute:      "IMAGE_REL_BASED_ABSOLUTE",
	RelocHigh:          "IMAGE_REL_BASED_HIGH",
	RelocLow:           "IMAGE_REL_BASED_LOW",
	RelocHighLow:       "IMAGE_REL_BASED_HIGHLOW",
	RelocHighAdj:       "IMAGE_REL_BASED_HIGHADJ",
	RelocMIPSJmpAddr:   "IMAGE_REL_BASED_MIPS_JMPADDR",
	RelocThumbMovT:     "IMAGE_REL_BASED_THUMB_MOV32",
	RelocRISCVLow12S:   "IMAGE_REL_BASED_RISCV_LOW12S",
	RelocMIPSJmpAddr16: "IMAGE_REL_BASED_MIPS_JMPADDR16",
	RelocDir64:         "IMAGE_REL_BASED_DIR64",
}

// Unpack unpacks the base relocation from binary data
func (br *BaseRelocation) Unpack(data []byte, offset uint32) error {
	br.SetFileOffset(offset)
	br.SetName("IMAGE_BASE_RELOCATION")

	if len(data) < int(offset)+8 { // Base relocation header is 8 bytes
		return NewPEFormatError("data too short for base relocation", offset)
	}

	return UnpackBinary(data, offset, br)
}

// Pack packs the base relocation into binary data
func (br *BaseRelocation) Pack() ([]byte, error) {
	return PackBinary(br)
}

// Size returns the size of the base relocation header
func (br *BaseRelocation) Size() int {
	return 8
}

// Dump returns a string representation of the base relocation
func (br *BaseRelocation) Dump() []string {
	lines := DumpStruct(br, br.name, 0)

	lines = append(lines, fmt.Sprintf("  Number of entries: %d", len(br.Entries)))

	if len(br.Entries) > 0 {
		lines = append(lines, "  Entries:")
		for i, entry := range br.Entries {
			if i >= 10 { // Limit output
				lines = append(lines, fmt.Sprintf("    ... and %d more", len(br.Entries)-i))
				break
			}

			typeName := RelocationTypeNames[entry.Type]
			if typeName == "" {
				typeName = fmt.Sprintf("Unknown(%d)", entry.Type)
			}

			lines = append(lines, fmt.Sprintf("    RVA: 0x%08X Type: %s Offset: 0x%04X",
				entry.RVA, typeName, entry.Offset))
		}
	}

	return lines
}

// DumpDict returns a dictionary representation of the base relocation
func (br *BaseRelocation) DumpDict() map[string]interface{} {
	dict := DumpStructDict(br)
	dict["NumberOfEntries"] = len(br.Entries)

	var entries []map[string]interface{}
	for _, entry := range br.Entries {
		entryDict := map[string]interface{}{
			"RVA":    entry.RVA,
			"Type":   entry.Type,
			"Offset": entry.Offset,
		}

		if typeName := RelocationTypeNames[entry.Type]; typeName != "" {
			entryDict["TypeName"] = typeName
		}

		entries = append(entries, entryDict)
	}
	dict["Entries"] = entries

	return dict
}

// GetTypeName returns the relocation type name
func (re *RelocationEntry) GetTypeName() string {
	if name := RelocationTypeNames[re.Type]; name != "" {
		return name
	}
	return fmt.Sprintf("Unknown(%d)", re.Type)
}
