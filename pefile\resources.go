package pefile

import (
	"fmt"
)

// ResourceDirectory represents the IMAGE_RESOURCE_DIRECTORY structure
type ResourceDirectory struct {
	BaseStructure
	Characteristics      uint32 // Reserved, must be 0
	TimeDateStamp        uint32 // Time and date stamp
	MajorVersion         uint16 // Major version number
	MinorVersion         uint16 // Minor version number
	NumberOfNamedEntries uint16 // Number of named entries
	NumberOfIdEntries    uint16 // Number of ID entries

	// Parsed data
	Entries []*ResourceDirEntry // Directory entries
}

// ResourceDirEntry represents the IMAGE_RESOURCE_DIRECTORY_ENTRY structure
type ResourceDirEntry struct {
	BaseStructure
	Name         uint32 // Name or ID
	OffsetToData uint32 // Offset to data or subdirectory

	// Parsed data
	NameString  string             // Name string (if named entry)
	ID          uint32             // ID (if ID entry)
	IsDirectory bool               // True if points to subdirectory
	Directory   *ResourceDirectory // Subdirectory (if IsDirectory is true)
	Data        *ResourceDataEntry // Data entry (if IsDirectory is false)
}

// ResourceDataEntry represents the IMAGE_RESOURCE_DATA_ENTRY structure
type ResourceDataEntry struct {
	BaseStructure
	OffsetToData uint32 // RVA to resource data
	DataSize     uint32 // Size of resource data
	CodePage     uint32 // Code page
	Reserved     uint32 // Reserved, must be 0

	// Parsed data
	Lang    uint16 // Primary language ID
	SubLang uint16 // Sublanguage ID
}

// Resource type constants
const (
	ResourceTypeCursor       = 1
	ResourceTypeBitmap       = 2
	ResourceTypeIcon         = 3
	ResourceTypeMenu         = 4
	ResourceTypeDialog       = 5
	ResourceTypeString       = 6
	ResourceTypeFontDir      = 7
	ResourceTypeFont         = 8
	ResourceTypeAccelerator  = 9
	ResourceTypeRCData       = 10
	ResourceTypeMessageTable = 11
	ResourceTypeGroupCursor  = 12
	ResourceTypeGroupIcon    = 14
	ResourceTypeVersion      = 16
	ResourceTypeDLGInclude   = 17
	ResourceTypePlugPlay     = 19
	ResourceTypeVXD          = 20
	ResourceTypeAniCursor    = 21
	ResourceTypeAniIcon      = 22
	ResourceTypeHTML         = 23
	ResourceTypeManifest     = 24
)

// ResourceTypeNames maps resource types to their string names
var ResourceTypeNames = map[uint32]string{
	ResourceTypeCursor:       "RT_CURSOR",
	ResourceTypeBitmap:       "RT_BITMAP",
	ResourceTypeIcon:         "RT_ICON",
	ResourceTypeMenu:         "RT_MENU",
	ResourceTypeDialog:       "RT_DIALOG",
	ResourceTypeString:       "RT_STRING",
	ResourceTypeFontDir:      "RT_FONTDIR",
	ResourceTypeFont:         "RT_FONT",
	ResourceTypeAccelerator:  "RT_ACCELERATOR",
	ResourceTypeRCData:       "RT_RCDATA",
	ResourceTypeMessageTable: "RT_MESSAGETABLE",
	ResourceTypeGroupCursor:  "RT_GROUP_CURSOR",
	ResourceTypeGroupIcon:    "RT_GROUP_ICON",
	ResourceTypeVersion:      "RT_VERSION",
	ResourceTypeDLGInclude:   "RT_DLGINCLUDE",
	ResourceTypePlugPlay:     "RT_PLUGPLAY",
	ResourceTypeVXD:          "RT_VXD",
	ResourceTypeAniCursor:    "RT_ANICURSOR",
	ResourceTypeAniIcon:      "RT_ANIICON",
	ResourceTypeHTML:         "RT_HTML",
	ResourceTypeManifest:     "RT_MANIFEST",
}

// Unpack unpacks the resource directory from binary data
func (rd *ResourceDirectory) Unpack(data []byte, offset uint32) error {
	rd.SetFileOffset(offset)
	rd.SetName("IMAGE_RESOURCE_DIRECTORY")

	if len(data) < int(offset)+16 { // Resource directory is 16 bytes
		return NewPEFormatError("data too short for resource directory", offset)
	}

	return UnpackBinary(data, offset, rd)
}

// Pack packs the resource directory into binary data
func (rd *ResourceDirectory) Pack() ([]byte, error) {
	return PackBinary(rd)
}

// Size returns the size of the resource directory
func (rd *ResourceDirectory) Size() int {
	return 16
}

// Dump returns a string representation of the resource directory
func (rd *ResourceDirectory) Dump() []string {
	lines := DumpStruct(rd, rd.name, 0)

	lines = append(lines, fmt.Sprintf("  Number of entries: %d", len(rd.Entries)))

	if len(rd.Entries) > 0 {
		lines = append(lines, "  Entries:")
		for i, entry := range rd.Entries {
			if i >= 10 { // Limit output
				lines = append(lines, fmt.Sprintf("    ... and %d more", len(rd.Entries)-i))
				break
			}

			if entry.NameString != "" {
				lines = append(lines, fmt.Sprintf("    Name: %s", entry.NameString))
			} else {
				typeName := ResourceTypeNames[entry.ID]
				if typeName != "" {
					lines = append(lines, fmt.Sprintf("    ID: %d (%s)", entry.ID, typeName))
				} else {
					lines = append(lines, fmt.Sprintf("    ID: %d", entry.ID))
				}
			}

			if entry.IsDirectory {
				lines = append(lines, "      -> Subdirectory")
			} else if entry.Data != nil {
				lines = append(lines, fmt.Sprintf("      -> Data (size: %d bytes)", entry.Data.DataSize))
			}
		}
	}

	return lines
}

// DumpDict returns a dictionary representation of the resource directory
func (rd *ResourceDirectory) DumpDict() map[string]interface{} {
	dict := DumpStructDict(rd)
	dict["NumberOfEntries"] = len(rd.Entries)

	var entries []map[string]interface{}
	for _, entry := range rd.Entries {
		entryDict := map[string]interface{}{
			"IsDirectory": entry.IsDirectory,
		}

		if entry.NameString != "" {
			entryDict["Name"] = entry.NameString
		} else {
			entryDict["ID"] = entry.ID
			if typeName := ResourceTypeNames[entry.ID]; typeName != "" {
				entryDict["TypeName"] = typeName
			}
		}

		if entry.Data != nil {
			entryDict["DataSize"] = entry.Data.DataSize
			entryDict["Lang"] = entry.Data.Lang
			entryDict["SubLang"] = entry.Data.SubLang
		}

		entries = append(entries, entryDict)
	}
	dict["Entries"] = entries

	return dict
}

// Unpack unpacks the resource directory entry from binary data
func (rde *ResourceDirEntry) Unpack(data []byte, offset uint32) error {
	rde.SetFileOffset(offset)
	rde.SetName("IMAGE_RESOURCE_DIRECTORY_ENTRY")

	if len(data) < int(offset)+8 { // Resource directory entry is 8 bytes
		return NewPEFormatError("data too short for resource directory entry", offset)
	}

	return UnpackBinary(data, offset, rde)
}

// Pack packs the resource directory entry into binary data
func (rde *ResourceDirEntry) Pack() ([]byte, error) {
	return PackBinary(rde)
}

// Size returns the size of the resource directory entry
func (rde *ResourceDirEntry) Size() int {
	return 8
}

// Dump returns a string representation of the resource directory entry
func (rde *ResourceDirEntry) Dump() []string {
	return DumpStruct(rde, rde.name, 0)
}

// DumpDict returns a dictionary representation of the resource directory entry
func (rde *ResourceDirEntry) DumpDict() map[string]interface{} {
	return DumpStructDict(rde)
}

// IsNamedEntry returns true if this is a named entry
func (rde *ResourceDirEntry) IsNamedEntry() bool {
	return rde.Name&0x80000000 != 0
}

// GetName returns the entry name or ID as a string
func (rde *ResourceDirEntry) GetName() string {
	if rde.NameString != "" {
		return rde.NameString
	}
	if typeName := ResourceTypeNames[rde.ID]; typeName != "" {
		return fmt.Sprintf("%d (%s)", rde.ID, typeName)
	}
	return fmt.Sprintf("%d", rde.ID)
}

// Unpack unpacks the resource data entry from binary data
func (rde *ResourceDataEntry) Unpack(data []byte, offset uint32) error {
	rde.SetFileOffset(offset)
	rde.SetName("IMAGE_RESOURCE_DATA_ENTRY")

	if len(data) < int(offset)+16 { // Resource data entry is 16 bytes
		return NewPEFormatError("data too short for resource data entry", offset)
	}

	return UnpackBinary(data, offset, rde)
}

// Pack packs the resource data entry into binary data
func (rde *ResourceDataEntry) Pack() ([]byte, error) {
	return PackBinary(rde)
}

// Size returns the size of the resource data entry
func (rde *ResourceDataEntry) Size() int {
	return 16
}

// Dump returns a string representation of the resource data entry
func (rde *ResourceDataEntry) Dump() []string {
	lines := DumpStruct(rde, rde.name, 0)

	lines = append(lines, fmt.Sprintf("  Language: %d", rde.Lang))
	lines = append(lines, fmt.Sprintf("  Sublanguage: %d", rde.SubLang))

	return lines
}

// DumpDict returns a dictionary representation of the resource data entry
func (rde *ResourceDataEntry) DumpDict() map[string]interface{} {
	dict := DumpStructDict(rde)
	dict["Language"] = rde.Lang
	dict["Sublanguage"] = rde.SubLang
	return dict
}

// GetResourceData returns the actual resource data
func (rde *ResourceDataEntry) GetResourceData(pe *PE) ([]byte, error) {
	if pe == nil {
		return nil, NewPEFormatError("no PE reference available", 0)
	}

	return pe.GetDataAtRVA(rde.OffsetToData, rde.DataSize)
}
