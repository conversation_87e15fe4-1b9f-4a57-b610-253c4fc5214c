package pefile

import (
	"fmt"
)

// Section represents a PE section
type Section struct {
	BaseStructure
	Name                 [8]byte               // Section name
	VirtualSize          uint32                // Virtual size
	VirtualAddress       uint32                // Virtual address
	SizeOfRawData        uint32                // Size of raw data
	PointerToRawData     uint32                // Pointer to raw data
	PointerToRelocations uint32                // Pointer to relocations
	PointerToLinenumbers uint32                // Pointer to line numbers
	NumberOfRelocations  uint16                // Number of relocations
	NumberOfLinenumbers  uint16                // Number of line numbers
	Characteristics      SectionCharacteristic // Section characteristics

	// Additional computed fields
	pe                       *PE    // Reference to parent PE
	adjustedPointerToRawData uint32 // Adjusted pointer to raw data
	adjustedVirtualAddress   uint32 // Adjusted virtual address
	sectionMinAddr           uint32 // Minimum address in section
	sectionMaxAddr           uint32 // Maximum address in section
}

// Unpack unpacks the section header from binary data (Structure interface)
func (s *Section) Unpack(data []byte, offset uint32) error {
	return s.UnpackWithPE(data, offset, nil)
}

// UnpackWithPE unpacks the section header from binary data with PE reference
func (s *Section) UnpackWithPE(data []byte, offset uint32, pe *PE) error {
	s.SetFileOffset(offset)
	s.SetName("IMAGE_SECTION_HEADER")
	s.pe = pe

	if len(data) < int(offset)+40 { // Section header is 40 bytes
		return NewPEFormatError("data too short for section header", offset)
	}

	if err := UnpackBinary(data, offset, s); err != nil {
		return err
	}

	// Calculate adjusted values
	s.adjustedPointerToRawData = s.adjustPointerToRawData()
	s.adjustedVirtualAddress = s.adjustVirtualAddress()

	// Calculate section bounds
	s.calculateSectionBounds()

	return nil
}

// Pack packs the section header into binary data
func (s *Section) Pack() ([]byte, error) {
	return PackBinary(s)
}

// Size returns the size of the section header
func (s *Section) Size() int {
	return 40
}

// Dump returns a string representation of the section
func (s *Section) Dump() []string {
	lines := []string{fmt.Sprintf("[%s]", s.name)}

	// Section name
	name := s.GetName()
	lines = append(lines, fmt.Sprintf("  Name: %s", name))

	// Basic fields
	lines = append(lines, fmt.Sprintf("  VirtualSize: 0x%08X (%d)", s.VirtualSize, s.VirtualSize))
	lines = append(lines, fmt.Sprintf("  VirtualAddress: 0x%08X", s.VirtualAddress))
	lines = append(lines, fmt.Sprintf("  SizeOfRawData: 0x%08X (%d)", s.SizeOfRawData, s.SizeOfRawData))
	lines = append(lines, fmt.Sprintf("  PointerToRawData: 0x%08X", s.PointerToRawData))
	lines = append(lines, fmt.Sprintf("  PointerToRelocations: 0x%08X", s.PointerToRelocations))
	lines = append(lines, fmt.Sprintf("  PointerToLinenumbers: 0x%08X", s.PointerToLinenumbers))
	lines = append(lines, fmt.Sprintf("  NumberOfRelocations: %d", s.NumberOfRelocations))
	lines = append(lines, fmt.Sprintf("  NumberOfLinenumbers: %d", s.NumberOfLinenumbers))
	lines = append(lines, fmt.Sprintf("  Characteristics: 0x%08X", s.Characteristics))

	// Characteristics flags
	lines = append(lines, "  Characteristics Flags:")
	if s.Characteristics&SectionCntCode != 0 {
		lines = append(lines, "    IMAGE_SCN_CNT_CODE")
	}
	if s.Characteristics&SectionCntInitializedData != 0 {
		lines = append(lines, "    IMAGE_SCN_CNT_INITIALIZED_DATA")
	}
	if s.Characteristics&SectionCntUninitializedData != 0 {
		lines = append(lines, "    IMAGE_SCN_CNT_UNINITIALIZED_DATA")
	}
	if s.Characteristics&SectionMemExecute != 0 {
		lines = append(lines, "    IMAGE_SCN_MEM_EXECUTE")
	}
	if s.Characteristics&SectionMemRead != 0 {
		lines = append(lines, "    IMAGE_SCN_MEM_READ")
	}
	if s.Characteristics&SectionMemWrite != 0 {
		lines = append(lines, "    IMAGE_SCN_MEM_WRITE")
	}
	if s.Characteristics&SectionMemDiscardable != 0 {
		lines = append(lines, "    IMAGE_SCN_MEM_DISCARDABLE")
	}
	if s.Characteristics&SectionMemShared != 0 {
		lines = append(lines, "    IMAGE_SCN_MEM_SHARED")
	}

	return lines
}

// DumpDict returns a dictionary representation of the section
func (s *Section) DumpDict() map[string]interface{} {
	dict := DumpStructDict(s)
	dict["Name"] = s.GetName()

	// Add characteristics flags
	var flags []string
	if s.Characteristics&SectionCntCode != 0 {
		flags = append(flags, "IMAGE_SCN_CNT_CODE")
	}
	if s.Characteristics&SectionCntInitializedData != 0 {
		flags = append(flags, "IMAGE_SCN_CNT_INITIALIZED_DATA")
	}
	if s.Characteristics&SectionCntUninitializedData != 0 {
		flags = append(flags, "IMAGE_SCN_CNT_UNINITIALIZED_DATA")
	}
	if s.Characteristics&SectionMemExecute != 0 {
		flags = append(flags, "IMAGE_SCN_MEM_EXECUTE")
	}
	if s.Characteristics&SectionMemRead != 0 {
		flags = append(flags, "IMAGE_SCN_MEM_READ")
	}
	if s.Characteristics&SectionMemWrite != 0 {
		flags = append(flags, "IMAGE_SCN_MEM_WRITE")
	}
	if s.Characteristics&SectionMemDiscardable != 0 {
		flags = append(flags, "IMAGE_SCN_MEM_DISCARDABLE")
	}
	if s.Characteristics&SectionMemShared != 0 {
		flags = append(flags, "IMAGE_SCN_MEM_SHARED")
	}
	dict["CharacteristicFlags"] = flags

	return dict
}

// GetName returns the section name as a string
func (s *Section) GetName() string {
	return BytesToString(s.Name[:])
}

// GetData returns the section's raw data
func (s *Section) GetData() ([]byte, error) {
	if s.pe == nil {
		return nil, NewPEFormatError("no PE reference available", 0)
	}

	if s.SizeOfRawData == 0 {
		return []byte{}, nil
	}

	start := s.adjustedPointerToRawData
	end := start + s.SizeOfRawData

	if end > uint32(len(s.pe.data)) {
		return nil, NewPEFormatError("section data extends beyond file", s.adjustedPointerToRawData)
	}

	return s.pe.data[start:end], nil
}

// ContainsRVA returns true if the section contains the given RVA
func (s *Section) ContainsRVA(rva uint32) bool {
	return rva >= s.adjustedVirtualAddress && rva < s.adjustedVirtualAddress+s.VirtualSize
}

// ContainsOffset returns true if the section contains the given file offset
func (s *Section) ContainsOffset(offset uint32) bool {
	if s.SizeOfRawData == 0 {
		return false
	}
	return offset >= s.adjustedPointerToRawData && offset < s.adjustedPointerToRawData+s.SizeOfRawData
}

// GetRVAFromOffset converts a file offset to an RVA within this section
func (s *Section) GetRVAFromOffset(offset uint32) (uint32, error) {
	if !s.ContainsOffset(offset) {
		return 0, NewPEFormatError("offset not in section", offset)
	}

	return s.adjustedVirtualAddress + (offset - s.adjustedPointerToRawData), nil
}

// GetOffsetFromRVA converts an RVA to a file offset within this section
func (s *Section) GetOffsetFromRVA(rva uint32) (uint32, error) {
	if !s.ContainsRVA(rva) {
		return 0, NewPEFormatError("RVA not in section", rva)
	}

	return s.adjustedPointerToRawData + (rva - s.adjustedVirtualAddress), nil
}

// adjustPointerToRawData adjusts the pointer to raw data based on file alignment
func (s *Section) adjustPointerToRawData() uint32 {
	if s.pe == nil {
		return s.PointerToRawData
	}

	var fileAlignment uint32
	if s.pe.OptionalHeader32 != nil {
		fileAlignment = s.pe.OptionalHeader32.FileAlignment
	} else if s.pe.OptionalHeader64 != nil {
		fileAlignment = s.pe.OptionalHeader64.FileAlignment
	} else {
		return s.PointerToRawData
	}

	// Align the pointer to raw data
	return AlignDown(s.PointerToRawData, fileAlignment)
}

// adjustVirtualAddress adjusts the virtual address based on section alignment
func (s *Section) adjustVirtualAddress() uint32 {
	if s.pe == nil {
		return s.VirtualAddress
	}

	var sectionAlignment, fileAlignment uint32
	if s.pe.OptionalHeader32 != nil {
		sectionAlignment = s.pe.OptionalHeader32.SectionAlignment
		fileAlignment = s.pe.OptionalHeader32.FileAlignment
	} else if s.pe.OptionalHeader64 != nil {
		sectionAlignment = s.pe.OptionalHeader64.SectionAlignment
		fileAlignment = s.pe.OptionalHeader64.FileAlignment
	} else {
		return s.VirtualAddress
	}

	// Adjust section alignment
	if sectionAlignment < 0x1000 { // page size
		sectionAlignment = fileAlignment
	}

	if sectionAlignment != 0 && s.VirtualAddress%sectionAlignment != 0 {
		return AlignDown(s.VirtualAddress, sectionAlignment)
	}

	return s.VirtualAddress
}

// calculateSectionBounds calculates the minimum and maximum addresses for the section
func (s *Section) calculateSectionBounds() {
	s.sectionMinAddr = s.adjustedVirtualAddress

	if s.VirtualSize > 0 {
		s.sectionMaxAddr = s.adjustedVirtualAddress + s.VirtualSize
	} else {
		s.sectionMaxAddr = s.adjustedVirtualAddress + s.SizeOfRawData
	}
}

// GetEntropy calculates the entropy of the section data
func (s *Section) GetEntropy() (float64, error) {
	data, err := s.GetData()
	if err != nil {
		return 0, err
	}

	if len(data) == 0 {
		return 0, nil
	}

	// Count byte frequencies
	freq := make([]int, 256)
	for _, b := range data {
		freq[b]++
	}

	// Calculate entropy
	entropy := 0.0
	length := float64(len(data))

	for _, count := range freq {
		if count > 0 {
			p := float64(count) / length
			entropy -= p * (float64(count) / length)
		}
	}

	return entropy, nil
}

// IsExecutable returns true if the section is executable
func (s *Section) IsExecutable() bool {
	return s.Characteristics&SectionMemExecute != 0
}

// IsWritable returns true if the section is writable
func (s *Section) IsWritable() bool {
	return s.Characteristics&SectionMemWrite != 0
}

// IsReadable returns true if the section is readable
func (s *Section) IsReadable() bool {
	return s.Characteristics&SectionMemRead != 0
}

// ContainsCode returns true if the section contains code
func (s *Section) ContainsCode() bool {
	return s.Characteristics&SectionCntCode != 0
}

// ContainsData returns true if the section contains initialized data
func (s *Section) ContainsData() bool {
	return s.Characteristics&SectionCntInitializedData != 0
}

// IsDiscardable returns true if the section is discardable
func (s *Section) IsDiscardable() bool {
	return s.Characteristics&SectionMemDiscardable != 0
}
