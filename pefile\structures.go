package pefile

import (
	"bytes"
	"encoding/binary"
	"fmt"
	"reflect"
	"strings"
)

// Structure represents a binary structure that can be unpacked from PE file data
type Structure interface {
	Unpack(data []byte, offset uint32) error
	Pack() ([]byte, error)
	Size() int
	FileOffset() uint32
	SetFileOffset(offset uint32)
	Dump() []string
	DumpDict() map[string]interface{}
	IsAllZeroes() bool
}

// BaseStructure provides common functionality for all PE structures
type BaseStructure struct {
	fileOffset uint32
	allZeroes  bool
	name       string
}

// FileOffset returns the file offset where this structure was read from
func (s *BaseStructure) FileOffset() uint32 {
	return s.fileOffset
}

// SetFileOffset sets the file offset for this structure
func (s *BaseStructure) SetFileOffset(offset uint32) {
	s.fileOffset = offset
}

// IsAllZeroes returns true if the structure data was all zeroes
func (s *BaseStructure) IsAllZeroes() bool {
	return s.allZeroes
}

// Name returns the structure name
func (s *BaseStructure) Name() string {
	return s.name
}

// SetName sets the structure name
func (s *BaseStructure) SetName(name string) {
	s.name = name
}

// UnpackBinary unpacks binary data into a struct using little-endian byte order
func UnpackBinary(data []byte, offset uint32, v interface{}) error {
	if len(data) < int(offset) {
		return NewPEFormatError("data too short for offset", offset)
	}

	reader := bytes.NewReader(data[offset:])

	// Handle structs with embedded BaseStructure
	val := reflect.ValueOf(v)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	// If this struct has a BaseStructure field, skip it and unpack only binary-compatible fields
	if val.NumField() > 0 && val.Field(0).Type().Name() == "BaseStructure" {
		// Get only the binary-compatible fields (skip BaseStructure and pointer fields)
		binaryFields := getBinaryCompatibleFields(val.Type())
		if len(binaryFields) == 0 {
			return nil // No binary fields to unpack
		}

		// Create a new struct with only binary-compatible fields
		newStruct := reflect.New(reflect.StructOf(binaryFields)).Interface()
		err := binary.Read(reader, binary.LittleEndian, newStruct)
		if err != nil {
			return err
		}

		// Copy the fields back to the original struct
		newVal := reflect.ValueOf(newStruct).Elem()
		binaryFieldIndex := 0
		for i := 1; i < val.NumField(); i++ { // Skip BaseStructure at index 0
			field := val.Type().Field(i)

			// Skip computed fields and pointers (same logic as getBinaryCompatibleFields)
			if field.Type.Kind() == reflect.Ptr {
				continue
			}

			// For Section struct, only include the actual PE header fields
			if val.Type().Name() == "Section" {
				if field.Name == "pe" || field.Name == "adjustedPointerToRawData" ||
					field.Name == "adjustedVirtualAddress" || field.Name == "sectionMinAddr" ||
					field.Name == "sectionMaxAddr" {
					continue
				}
			}

			// For ResourceDirEntry struct, only include the actual PE header fields
			if val.Type().Name() == "ResourceDirEntry" {
				if field.Name == "NameString" || field.Name == "ID" || field.Name == "IsDirectory" ||
					field.Name == "Directory" || field.Name == "Data" {
					continue
				}
			}

			// For ResourceDataEntry struct, only include the actual PE header fields
			if val.Type().Name() == "ResourceDataEntry" {
				if field.Name == "Lang" || field.Name == "SubLang" {
					continue
				}
			}

			if isBinaryCompatible(field.Type) && binaryFieldIndex < newVal.NumField() {
				val.Field(i).Set(newVal.Field(binaryFieldIndex))
				binaryFieldIndex++
			}
		}
		return nil
	}

	return binary.Read(reader, binary.LittleEndian, v)
}

// getFieldsExceptFirst returns struct fields except the first one
func getFieldsExceptFirst(t reflect.Type) []reflect.StructField {
	var fields []reflect.StructField
	for i := 1; i < t.NumField(); i++ {
		fields = append(fields, t.Field(i))
	}
	return fields
}

// getBinaryCompatibleFields returns only fields that can be binary unpacked
func getBinaryCompatibleFields(t reflect.Type) []reflect.StructField {
	var fields []reflect.StructField
	for i := 1; i < t.NumField(); i++ { // Skip BaseStructure at index 0
		field := t.Field(i)

		// Skip computed fields and pointers
		if field.Type.Kind() == reflect.Ptr {
			continue
		}

		// For Section struct, only include the actual PE header fields
		if t.Name() == "Section" {
			// Only include fields up to Characteristics (the actual PE section header)
			if field.Name == "pe" || field.Name == "adjustedPointerToRawData" ||
				field.Name == "adjustedVirtualAddress" || field.Name == "sectionMinAddr" ||
				field.Name == "sectionMaxAddr" {
				continue
			}
		}

		// For ResourceDirEntry struct, only include the actual PE header fields
		if t.Name() == "ResourceDirEntry" {
			if field.Name == "NameString" || field.Name == "ID" || field.Name == "IsDirectory" ||
				field.Name == "Directory" || field.Name == "Data" {
				continue
			}
		}

		// For ResourceDataEntry struct, only include the actual PE header fields
		if t.Name() == "ResourceDataEntry" {
			if field.Name == "Lang" || field.Name == "SubLang" {
				continue
			}
		}

		if isBinaryCompatible(field.Type) {
			fields = append(fields, field)
		}
	}
	return fields
}

// isBinaryCompatible checks if a type can be binary unpacked
func isBinaryCompatible(t reflect.Type) bool {
	switch t.Kind() {
	case reflect.Bool, reflect.Int8, reflect.Uint8, reflect.Int16, reflect.Uint16,
		reflect.Int32, reflect.Uint32, reflect.Int64, reflect.Uint64,
		reflect.Float32, reflect.Float64:
		return true
	case reflect.Array:
		return isBinaryCompatible(t.Elem())
	case reflect.Struct:
		// Check if it's a simple struct with only binary-compatible fields
		for i := 0; i < t.NumField(); i++ {
			if !isBinaryCompatible(t.Field(i).Type) {
				return false
			}
		}
		return true
	case reflect.Ptr, reflect.Slice, reflect.Map, reflect.Chan, reflect.Func, reflect.Interface:
		return false
	default:
		return false
	}
}

// PackBinary packs a struct into binary data using little-endian byte order
func PackBinary(v interface{}) ([]byte, error) {
	var buf bytes.Buffer
	err := binary.Write(&buf, binary.LittleEndian, v)
	if err != nil {
		return nil, err
	}
	return buf.Bytes(), nil
}

// GetStructSize returns the size of a struct in bytes
func GetStructSize(v interface{}) int {
	return int(binary.Size(v))
}

// IsAllZeroes checks if a byte slice contains only zero bytes
func IsAllZeroes(data []byte) bool {
	for _, b := range data {
		if b != 0 {
			return false
		}
	}
	return true
}

// DumpStruct creates a string representation of a struct's fields
func DumpStruct(v interface{}, name string, indent int) []string {
	var lines []string
	indentStr := strings.Repeat("  ", indent)

	lines = append(lines, fmt.Sprintf("%s[%s]", indentStr, name))

	val := reflect.ValueOf(v)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	typ := val.Type()
	for i := 0; i < val.NumField(); i++ {
		field := val.Field(i)
		fieldType := typ.Field(i)

		// Skip unexported fields
		if !field.CanInterface() {
			continue
		}

		fieldName := fieldType.Name
		fieldValue := field.Interface()

		switch field.Kind() {
		case reflect.Uint8:
			lines = append(lines, fmt.Sprintf("%s  %s: 0x%02X (%d)", indentStr, fieldName, fieldValue, fieldValue))
		case reflect.Uint16:
			lines = append(lines, fmt.Sprintf("%s  %s: 0x%04X (%d)", indentStr, fieldName, fieldValue, fieldValue))
		case reflect.Uint32:
			lines = append(lines, fmt.Sprintf("%s  %s: 0x%08X (%d)", indentStr, fieldName, fieldValue, fieldValue))
		case reflect.Uint64:
			lines = append(lines, fmt.Sprintf("%s  %s: 0x%016X (%d)", indentStr, fieldName, fieldValue, fieldValue))
		case reflect.Array:
			if field.Type().Elem().Kind() == reflect.Uint8 {
				// Handle byte arrays specially
				bytes := make([]byte, field.Len())
				for j := 0; j < field.Len(); j++ {
					bytes[j] = uint8(field.Index(j).Uint())
				}
				// Try to display as string if printable
				if isPrintableBytes(bytes) {
					lines = append(lines, fmt.Sprintf("%s  %s: %q", indentStr, fieldName, string(bytes)))
				} else {
					lines = append(lines, fmt.Sprintf("%s  %s: %v", indentStr, fieldName, bytes))
				}
			} else {
				lines = append(lines, fmt.Sprintf("%s  %s: %v", indentStr, fieldName, fieldValue))
			}
		default:
			lines = append(lines, fmt.Sprintf("%s  %s: %v", indentStr, fieldName, fieldValue))
		}
	}

	return lines
}

// DumpStructDict creates a dictionary representation of a struct's fields
func DumpStructDict(v interface{}) map[string]interface{} {
	result := make(map[string]interface{})

	val := reflect.ValueOf(v)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	typ := val.Type()
	for i := 0; i < val.NumField(); i++ {
		field := val.Field(i)
		fieldType := typ.Field(i)

		// Skip unexported fields
		if !field.CanInterface() {
			continue
		}

		fieldName := fieldType.Name
		fieldValue := field.Interface()

		switch field.Kind() {
		case reflect.Array:
			if field.Type().Elem().Kind() == reflect.Uint8 {
				// Handle byte arrays specially
				bytes := make([]byte, field.Len())
				for j := 0; j < field.Len(); j++ {
					bytes[j] = uint8(field.Index(j).Uint())
				}
				result[fieldName] = bytes
			} else {
				result[fieldName] = fieldValue
			}
		default:
			result[fieldName] = fieldValue
		}
	}

	return result
}

// isPrintableBytes checks if a byte array contains only printable ASCII characters
func isPrintableBytes(data []byte) bool {
	for _, b := range data {
		if b == 0 {
			break // Null terminator
		}
		if b < 32 || b > 126 {
			return false
		}
	}
	return true
}

// PadBytes pads a byte slice to the specified length with zero bytes
func PadBytes(data []byte, length int) []byte {
	if len(data) >= length {
		return data[:length]
	}

	result := make([]byte, length)
	copy(result, data)
	return result
}

// TrimNullBytes removes trailing null bytes from a byte slice
func TrimNullBytes(data []byte) []byte {
	for i := len(data) - 1; i >= 0; i-- {
		if data[i] != 0 {
			return data[:i+1]
		}
	}
	return []byte{}
}

// BytesToString converts a null-terminated byte array to a string
func BytesToString(data []byte) string {
	// Find the first null byte
	for i, b := range data {
		if b == 0 {
			return string(data[:i])
		}
	}
	return string(data)
}

// AlignUp aligns a value up to the specified alignment
func AlignUp(value, alignment uint32) uint32 {
	if alignment == 0 {
		return value
	}
	return ((value + alignment - 1) / alignment) * alignment
}

// AlignDown aligns a value down to the specified alignment
func AlignDown(value, alignment uint32) uint32 {
	if alignment == 0 {
		return value
	}
	return (value / alignment) * alignment
}
