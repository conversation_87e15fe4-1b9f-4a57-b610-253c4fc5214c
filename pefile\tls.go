package pefile

import (
	"fmt"
)

// TLSDirectory32 represents the IMAGE_TLS_DIRECTORY32 structure
type TLSDirectory32 struct {
	BaseStructure
	StartAddressOfRawData uint32 // VA of start of TLS data
	EndAddressOfRawData   uint32 // VA of end of TLS data
	AddressOfIndex        uint32 // VA of TLS index
	AddressOfCallBacks    uint32 // VA of TLS callbacks
	SizeOfZeroFill        uint32 // Size of zero fill
	Characteristics       uint32 // Characteristics

	// Parsed data
	Callbacks []uint32 // TLS callback addresses
}

// TLSDirectory64 represents the IMAGE_TLS_DIRECTORY64 structure
type TLSDirectory64 struct {
	BaseStructure
	StartAddressOfRawData uint64 // VA of start of TLS data
	EndAddressOfRawData   uint64 // VA of end of TLS data
	AddressOfIndex        uint64 // VA of TLS index
	AddressOfCallBacks    uint64 // VA of TLS callbacks
	SizeOfZeroFill        uint32 // Size of zero fill
	Characteristics       uint32 // Characteristics

	// Parsed data
	Callbacks []uint64 // TLS callback addresses
}

// TLSDirectory is a generic interface for TLS directories
type TLSDirectory interface {
	Structure
	GetCallbacks() interface{}
	GetSizeOfZeroFill() uint32
	GetCharacteristics() uint32
}

// GetCallbacks returns the TLS callbacks
func (tls *TLSDirectory32) GetCallbacks() interface{} {
	return tls.Callbacks
}

// GetSizeOfZeroFill returns the size of zero fill
func (tls *TLSDirectory32) GetSizeOfZeroFill() uint32 {
	return tls.SizeOfZeroFill
}

// GetCharacteristics returns the characteristics
func (tls *TLSDirectory32) GetCharacteristics() uint32 {
	return tls.Characteristics
}

// GetCallbacks returns the TLS callbacks
func (tls *TLSDirectory64) GetCallbacks() interface{} {
	return tls.Callbacks
}

// GetSizeOfZeroFill returns the size of zero fill
func (tls *TLSDirectory64) GetSizeOfZeroFill() uint32 {
	return tls.SizeOfZeroFill
}

// GetCharacteristics returns the characteristics
func (tls *TLSDirectory64) GetCharacteristics() uint32 {
	return tls.Characteristics
}

// Unpack unpacks the 32-bit TLS directory from binary data
func (tls *TLSDirectory32) Unpack(data []byte, offset uint32) error {
	tls.SetFileOffset(offset)
	tls.SetName("IMAGE_TLS_DIRECTORY32")

	if len(data) < int(offset)+24 { // TLS directory 32 is 24 bytes
		return NewPEFormatError("data too short for TLS directory 32", offset)
	}

	return UnpackBinary(data, offset, tls)
}

// Pack packs the 32-bit TLS directory into binary data
func (tls *TLSDirectory32) Pack() ([]byte, error) {
	return PackBinary(tls)
}

// Size returns the size of the 32-bit TLS directory
func (tls *TLSDirectory32) Size() int {
	return 24
}

// Dump returns a string representation of the 32-bit TLS directory
func (tls *TLSDirectory32) Dump() []string {
	lines := DumpStruct(tls, tls.name, 0)

	lines = append(lines, fmt.Sprintf("  TLS Data Size: %d bytes",
		tls.EndAddressOfRawData-tls.StartAddressOfRawData))
	lines = append(lines, fmt.Sprintf("  Number of callbacks: %d", len(tls.Callbacks)))

	if len(tls.Callbacks) > 0 {
		lines = append(lines, "  Callbacks:")
		for i, callback := range tls.Callbacks {
			if i >= 10 { // Limit output
				lines = append(lines, fmt.Sprintf("    ... and %d more", len(tls.Callbacks)-i))
				break
			}
			lines = append(lines, fmt.Sprintf("    0x%08X", callback))
		}
	}

	return lines
}

// DumpDict returns a dictionary representation of the 32-bit TLS directory
func (tls *TLSDirectory32) DumpDict() map[string]interface{} {
	dict := DumpStructDict(tls)
	dict["TLSDataSize"] = tls.EndAddressOfRawData - tls.StartAddressOfRawData
	dict["NumberOfCallbacks"] = len(tls.Callbacks)
	dict["Callbacks"] = tls.Callbacks
	return dict
}

// Unpack unpacks the 64-bit TLS directory from binary data
func (tls *TLSDirectory64) Unpack(data []byte, offset uint32) error {
	tls.SetFileOffset(offset)
	tls.SetName("IMAGE_TLS_DIRECTORY64")

	if len(data) < int(offset)+40 { // TLS directory 64 is 40 bytes
		return NewPEFormatError("data too short for TLS directory 64", offset)
	}

	return UnpackBinary(data, offset, tls)
}

// Pack packs the 64-bit TLS directory into binary data
func (tls *TLSDirectory64) Pack() ([]byte, error) {
	return PackBinary(tls)
}

// Size returns the size of the 64-bit TLS directory
func (tls *TLSDirectory64) Size() int {
	return 40
}

// Dump returns a string representation of the 64-bit TLS directory
func (tls *TLSDirectory64) Dump() []string {
	lines := DumpStruct(tls, tls.name, 0)

	lines = append(lines, fmt.Sprintf("  TLS Data Size: %d bytes",
		tls.EndAddressOfRawData-tls.StartAddressOfRawData))
	lines = append(lines, fmt.Sprintf("  Number of callbacks: %d", len(tls.Callbacks)))

	if len(tls.Callbacks) > 0 {
		lines = append(lines, "  Callbacks:")
		for i, callback := range tls.Callbacks {
			if i >= 10 { // Limit output
				lines = append(lines, fmt.Sprintf("    ... and %d more", len(tls.Callbacks)-i))
				break
			}
			lines = append(lines, fmt.Sprintf("    0x%016X", callback))
		}
	}

	return lines
}

// DumpDict returns a dictionary representation of the 64-bit TLS directory
func (tls *TLSDirectory64) DumpDict() map[string]interface{} {
	dict := DumpStructDict(tls)
	dict["TLSDataSize"] = tls.EndAddressOfRawData - tls.StartAddressOfRawData
	dict["NumberOfCallbacks"] = len(tls.Callbacks)
	dict["Callbacks"] = tls.Callbacks
	return dict
}
