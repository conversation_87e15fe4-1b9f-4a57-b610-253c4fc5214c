package pefile

import (
	"fmt"
	"math"
	"unicode"
)

// CalculateEntropy calculates the Shannon entropy of data
func CalculateEntropy(data []byte) float64 {
	if len(data) == 0 {
		return 0
	}

	// Count byte frequencies
	freq := make([]int, 256)
	for _, b := range data {
		freq[b]++
	}

	// Calculate entropy
	entropy := 0.0
	length := float64(len(data))

	for _, count := range freq {
		if count > 0 {
			p := float64(count) / length
			entropy -= p * math.Log2(p)
		}
	}

	return entropy
}

// IsPrintableASCII checks if a byte slice contains only printable ASCII characters
func IsPrintableASCII(data []byte) bool {
	for _, b := range data {
		if b < 32 || b > 126 {
			return false
		}
	}
	return true
}

// IsValidIdentifier checks if a string is a valid identifier (alphanumeric + underscore)
func IsValidIdentifier(s string) bool {
	if len(s) == 0 {
		return false
	}

	for i, r := range s {
		if i == 0 {
			if !unicode.IsLetter(r) && r != '_' {
				return false
			}
		} else {
			if !unicode.IsLetter(r) && !unicode.IsDigit(r) && r != '_' {
				return false
			}
		}
	}
	return true
}

// FormatHex formats a number as a hexadecimal string with 0x prefix
func FormatHex(value interface{}) string {
	switch v := value.(type) {
	case uint8:
		return fmt.Sprintf("0x%02X", v)
	case uint16:
		return fmt.Sprintf("0x%04X", v)
	case uint32:
		return fmt.Sprintf("0x%08X", v)
	case uint64:
		return fmt.Sprintf("0x%016X", v)
	case int8:
		return fmt.Sprintf("0x%02X", uint8(v))
	case int16:
		return fmt.Sprintf("0x%04X", uint16(v))
	case int32:
		return fmt.Sprintf("0x%08X", uint32(v))
	case int64:
		return fmt.Sprintf("0x%016X", uint64(v))
	default:
		return fmt.Sprintf("0x%X", v)
	}
}

// GetMachineTypeName returns the machine type name for a given machine type
func GetMachineTypeName(machineType MachineType) string {
	if name, ok := MachineTypeNames[machineType]; ok {
		return name
	}
	return fmt.Sprintf("Unknown(0x%04X)", machineType)
}

// GetSubsystemName returns the subsystem name for a given subsystem type
func GetSubsystemName(subsystem SubsystemType) string {
	if name, ok := SubsystemTypeNames[subsystem]; ok {
		return name
	}
	return fmt.Sprintf("Unknown(0x%04X)", subsystem)
}

// GetSectionCharacteristicsNames returns the names of set section characteristics
func GetSectionCharacteristicsNames(characteristics SectionCharacteristic) []string {
	var names []string

	for flag, name := range SectionCharacteristicNames {
		if characteristics&flag != 0 {
			names = append(names, name)
		}
	}

	return names
}

// GetImageCharacteristicsNames returns the names of set image characteristics
func GetImageCharacteristicsNames(characteristics ImageCharacteristic) []string {
	var names []string

	for flag, name := range ImageCharacteristicNames {
		if characteristics&flag != 0 {
			names = append(names, name)
		}
	}

	return names
}

// IsValidPESignature checks if the data starts with a valid PE signature
func IsValidPESignature(data []byte) bool {
	if len(data) < 64 {
		return false
	}

	// Check DOS signature
	if data[0] != 0x4D || data[1] != 0x5A { // "MZ"
		return false
	}

	// Get e_lfanew
	lfanew := uint32(data[60]) | uint32(data[61])<<8 | uint32(data[62])<<16 | uint32(data[63])<<24

	if lfanew >= uint32(len(data))-4 {
		return false
	}

	// Check PE signature
	if data[lfanew] != 0x50 || data[lfanew+1] != 0x45 || data[lfanew+2] != 0x00 || data[lfanew+3] != 0x00 { // "PE\0\0"
		return false
	}

	return true
}

// GetFileTypeDescription returns a human-readable description of the file type
func GetFileTypeDescription(pe *PE) string {
	if pe.FileHeader == nil {
		return "Unknown"
	}

	characteristics := pe.FileHeader.Characteristics

	if characteristics&ImageFileDLL != 0 {
		return "Dynamic Link Library (DLL)"
	} else if characteristics&ImageFileExecutableImage != 0 {
		if characteristics&ImageFileSystem != 0 {
			return "System File"
		}
		return "Executable (EXE)"
	} else {
		return "Object File"
	}
}

// GetArchitectureDescription returns a human-readable description of the architecture
func GetArchitectureDescription(pe *PE) string {
	if pe.FileHeader == nil {
		return "Unknown"
	}

	machine := pe.FileHeader.Machine

	switch machine {
	case MachineI386:
		return "Intel 386 (32-bit)"
	case MachineAMD64:
		return "AMD64 (64-bit)"
	case MachineARM:
		return "ARM (32-bit)"
	case MachineARM64:
		return "ARM64 (64-bit)"
	case MachineIA64:
		return "Intel Itanium (64-bit)"
	default:
		if name := GetMachineTypeName(machine); name != "" {
			return name
		}
		return fmt.Sprintf("Unknown (0x%04X)", machine)
	}
}
