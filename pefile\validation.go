package pefile

import (
	"fmt"
)

// ValidationResult represents the result of a validation check
type ValidationResult struct {
	IsValid  bool
	Errors   []string
	Warnings []string
}

// AddError adds an error to the validation result
func (vr *ValidationResult) AddError(message string) {
	vr.IsValid = false
	vr.Errors = append(vr.Errors, message)
}

// AddWarning adds a warning to the validation result
func (vr *ValidationResult) AddWarning(message string) {
	vr.Warnings = append(vr.Warnings, message)
}

// HasIssues returns true if there are any errors or warnings
func (vr *ValidationResult) HasIssues() bool {
	return len(vr.Errors) > 0 || len(vr.Warnings) > 0
}

// ValidatePE performs comprehensive validation of a PE file
func (pe *PE) ValidatePE() *ValidationResult {
	result := &ValidationResult{IsValid: true}

	// Validate DOS header
	pe.validateDOSHeader(result)

	// Validate NT headers
	pe.validateNTHeaders(result)

	// Validate file header
	pe.validateFileHeader(result)

	// Validate optional header
	pe.validateOptionalHeader(result)

	// Validate sections
	pe.validateSections(result)

	// Validate data directories
	pe.validateDataDirectories(result)

	// Validate imports
	pe.validateImports(result)

	// Validate exports
	pe.validateExports(result)

	// Validate resources
	pe.validateResources(result)

	// Validate relocations
	pe.validateRelocations(result)

	// Validate overall file structure
	pe.validateFileStructure(result)

	return result
}

// validateDOSHeader validates the DOS header
func (pe *PE) validateDOSHeader(result *ValidationResult) {
	if pe.DOSHeader == nil {
		result.AddError("DOS header is missing")
		return
	}

	// Check DOS signature
	if pe.DOSHeader.Magic != ImageDOSSignature {
		result.AddError(fmt.Sprintf("invalid DOS signature: 0x%04X (expected 0x%04X)",
			pe.DOSHeader.Magic, ImageDOSSignature))
	}

	// Check e_lfanew bounds
	if pe.DOSHeader.NewHeaderAddr >= uint32(len(pe.data)) {
		result.AddError(fmt.Sprintf("e_lfanew points beyond file: 0x%08X", pe.DOSHeader.NewHeaderAddr))
	}

	// Check reasonable e_lfanew value
	if pe.DOSHeader.NewHeaderAddr < 0x40 {
		result.AddWarning(fmt.Sprintf("e_lfanew is unusually small: 0x%08X", pe.DOSHeader.NewHeaderAddr))
	}

	if pe.DOSHeader.NewHeaderAddr > 0x1000 {
		result.AddWarning(fmt.Sprintf("e_lfanew is unusually large: 0x%08X", pe.DOSHeader.NewHeaderAddr))
	}
}

// validateNTHeaders validates the NT headers
func (pe *PE) validateNTHeaders(result *ValidationResult) {
	if pe.NTHeaders == nil {
		result.AddError("NT headers are missing")
		return
	}

	// Check NT signature
	if pe.NTHeaders.Signature != ImageNTSignature {
		result.AddError(fmt.Sprintf("invalid NT signature: 0x%08X (expected 0x%08X)",
			pe.NTHeaders.Signature, ImageNTSignature))
	}
}

// validateFileHeader validates the file header
func (pe *PE) validateFileHeader(result *ValidationResult) {
	if pe.FileHeader == nil {
		result.AddError("file header is missing")
		return
	}

	// Check machine type
	if pe.FileHeader.Machine == 0 {
		result.AddWarning("machine type is 0 (unknown)")
	}

	// Check number of sections
	if pe.FileHeader.NumberOfSections == 0 {
		result.AddWarning("file has no sections")
	} else if pe.FileHeader.NumberOfSections > 96 {
		result.AddWarning(fmt.Sprintf("unusually high number of sections: %d", pe.FileHeader.NumberOfSections))
	}

	// Check timestamp
	if pe.FileHeader.TimeDateStamp == 0 {
		result.AddWarning("timestamp is 0")
	}

	// Check characteristics
	if pe.FileHeader.Characteristics&ImageFileExecutableImage == 0 {
		result.AddWarning("IMAGE_FILE_EXECUTABLE_IMAGE flag not set")
	}
}

// validateOptionalHeader validates the optional header
func (pe *PE) validateOptionalHeader(result *ValidationResult) {
	if pe.OptionalHeader32 == nil && pe.OptionalHeader64 == nil {
		result.AddError("optional header is missing")
		return
	}

	var magic uint16
	var imageBase uint64
	var sectionAlignment uint32
	var fileAlignment uint32
	var sizeOfImage uint32
	var sizeOfHeaders uint32

	if pe.OptionalHeader32 != nil {
		magic = pe.OptionalHeader32.Magic
		imageBase = uint64(pe.OptionalHeader32.ImageBase)
		sectionAlignment = pe.OptionalHeader32.SectionAlignment
		fileAlignment = pe.OptionalHeader32.FileAlignment
		sizeOfImage = pe.OptionalHeader32.SizeOfImage
		sizeOfHeaders = pe.OptionalHeader32.SizeOfHeaders
	} else {
		magic = pe.OptionalHeader64.Magic
		imageBase = pe.OptionalHeader64.ImageBase
		sectionAlignment = pe.OptionalHeader64.SectionAlignment
		fileAlignment = pe.OptionalHeader64.FileAlignment
		sizeOfImage = pe.OptionalHeader64.SizeOfImage
		sizeOfHeaders = pe.OptionalHeader64.SizeOfHeaders
	}

	// Check magic
	if magic != OptionalHeaderMagicPE && magic != OptionalHeaderMagicPEPlus {
		result.AddError(fmt.Sprintf("invalid optional header magic: 0x%04X", magic))
	}

	// Check image base alignment
	if imageBase%0x10000 != 0 {
		result.AddWarning(fmt.Sprintf("image base not aligned to 64KB: 0x%016X", imageBase))
	}

	// Check section alignment
	if sectionAlignment < fileAlignment {
		result.AddError(fmt.Sprintf("section alignment (0x%X) < file alignment (0x%X)",
			sectionAlignment, fileAlignment))
	}

	// Check alignment values are powers of 2
	if sectionAlignment&(sectionAlignment-1) != 0 {
		result.AddWarning(fmt.Sprintf("section alignment is not a power of 2: 0x%X", sectionAlignment))
	}

	if fileAlignment&(fileAlignment-1) != 0 {
		result.AddWarning(fmt.Sprintf("file alignment is not a power of 2: 0x%X", fileAlignment))
	}

	// Check size of image
	if sizeOfImage == 0 {
		result.AddError("size of image is 0")
	}

	// Check size of headers
	if sizeOfHeaders == 0 {
		result.AddError("size of headers is 0")
	} else if sizeOfHeaders > uint32(len(pe.data)) {
		result.AddError(fmt.Sprintf("size of headers (0x%X) exceeds file size", sizeOfHeaders))
	}
}

// validateSections validates the sections
func (pe *PE) validateSections(result *ValidationResult) {
	if len(pe.Sections) == 0 {
		result.AddWarning("file has no sections")
		return
	}

	// Check section count matches file header
	if pe.FileHeader != nil && len(pe.Sections) != int(pe.FileHeader.NumberOfSections) {
		result.AddError(fmt.Sprintf("section count mismatch: header says %d, found %d",
			pe.FileHeader.NumberOfSections, len(pe.Sections)))
	}

	for i, section := range pe.Sections {
		if section == nil {
			result.AddError(fmt.Sprintf("section %d is nil", i))
			continue
		}

		// Check section name
		name := section.GetName()
		if name == "" {
			result.AddWarning(fmt.Sprintf("section %d has empty name", i))
		}

		// Check virtual address alignment
		var sectionAlignment uint32 = 0x1000 // Default
		if pe.OptionalHeader32 != nil && pe.OptionalHeader32.SectionAlignment > 0 {
			sectionAlignment = pe.OptionalHeader32.SectionAlignment
		} else if pe.OptionalHeader64 != nil && pe.OptionalHeader64.SectionAlignment > 0 {
			sectionAlignment = pe.OptionalHeader64.SectionAlignment
		}

		if sectionAlignment > 0 && section.VirtualAddress%sectionAlignment != 0 {
			result.AddWarning(fmt.Sprintf("section %d virtual address not aligned: 0x%08X",
				i, section.VirtualAddress))
		}

		// Check for overlapping sections
		for j := i + 1; j < len(pe.Sections); j++ {
			other := pe.Sections[j]
			if other == nil {
				continue
			}

			// Check virtual address overlap
			if section.VirtualAddress < other.VirtualAddress+other.VirtualSize &&
				other.VirtualAddress < section.VirtualAddress+section.VirtualSize {
				result.AddError(fmt.Sprintf("sections %d and %d overlap in virtual space", i, j))
			}

			// Check raw data overlap
			if section.PointerToRawData > 0 && other.PointerToRawData > 0 &&
				section.PointerToRawData < other.PointerToRawData+other.SizeOfRawData &&
				other.PointerToRawData < section.PointerToRawData+section.SizeOfRawData {
				result.AddError(fmt.Sprintf("sections %d and %d overlap in file", i, j))
			}
		}

		// Check section bounds
		if section.PointerToRawData+section.SizeOfRawData > uint32(len(pe.data)) {
			result.AddError(fmt.Sprintf("section %d extends beyond file: 0x%08X + 0x%08X",
				i, section.PointerToRawData, section.SizeOfRawData))
		}
	}
}

// validateDataDirectories validates the data directories
func (pe *PE) validateDataDirectories(result *ValidationResult) {
	var dataDirectories []DataDirectory

	if pe.OptionalHeader32 != nil {
		dataDirectories = pe.OptionalHeader32.DataDirectories[:]
	} else if pe.OptionalHeader64 != nil {
		dataDirectories = pe.OptionalHeader64.DataDirectories[:]
	} else {
		return
	}

	for i, dir := range dataDirectories {
		if dir.VirtualAddress == 0 && dir.Size == 0 {
			continue // Empty directory is OK
		}

		if dir.VirtualAddress == 0 && dir.Size > 0 {
			result.AddWarning(fmt.Sprintf("data directory %d has size but no RVA", i))
		}

		if dir.VirtualAddress > 0 && dir.Size == 0 {
			result.AddWarning(fmt.Sprintf("data directory %d has RVA but no size", i))
		}

		// Check if RVA is within image
		if _, err := pe.GetOffsetFromRVA(dir.VirtualAddress); err != nil {
			result.AddError(fmt.Sprintf("data directory %d RVA 0x%08X is invalid", i, dir.VirtualAddress))
		}
	}
}

// validateImports validates the import table
func (pe *PE) validateImports(result *ValidationResult) {
	for i, imp := range pe.Imports {
		if imp == nil {
			result.AddError(fmt.Sprintf("import descriptor %d is nil", i))
			continue
		}

		if imp.DLL == "" {
			result.AddWarning(fmt.Sprintf("import descriptor %d has empty DLL name", i))
		}

		if len(imp.Imports) == 0 {
			result.AddWarning(fmt.Sprintf("import descriptor %d (%s) has no imports", i, imp.DLL))
		}
	}
}

// validateExports validates the export table
func (pe *PE) validateExports(result *ValidationResult) {
	if pe.ExportDirectory == nil {
		return // No exports is OK
	}

	if pe.ExportDirectory.DLLName == "" {
		result.AddWarning("export directory has empty DLL name")
	}

	if len(pe.ExportDirectory.Exports) == 0 {
		result.AddWarning("export directory has no exports")
	}
}

// validateResources validates the resource directory
func (pe *PE) validateResources(result *ValidationResult) {
	if pe.ResourceDirectory == nil {
		return // No resources is OK
	}

	if len(pe.ResourceDirectory.Entries) == 0 {
		result.AddWarning("resource directory has no entries")
	}
}

// validateRelocations validates the relocation table
func (pe *PE) validateRelocations(result *ValidationResult) {
	for i, reloc := range pe.Relocations {
		if reloc == nil {
			result.AddError(fmt.Sprintf("relocation block %d is nil", i))
			continue
		}

		if len(reloc.Entries) == 0 {
			result.AddWarning(fmt.Sprintf("relocation block %d has no entries", i))
		}
	}
}

// validateFileStructure validates overall file structure
func (pe *PE) validateFileStructure(result *ValidationResult) {
	// Check file size
	if len(pe.data) == 0 {
		result.AddError("file is empty")
		return
	}

	// Check minimum file size
	if len(pe.data) < 64 {
		result.AddError("file too small to be a valid PE")
		return
	}

	// Check for common malware indicators
	if len(pe.Sections) > 20 {
		result.AddWarning("unusually high number of sections (possible packer)")
	}

	// Check entropy of sections
	for i, section := range pe.Sections {
		if section == nil {
			continue
		}

		data, err := section.GetData()
		if err != nil {
			continue
		}

		entropy := CalculateEntropy(data)
		if entropy > 7.5 {
			result.AddWarning(fmt.Sprintf("section %d (%s) has high entropy %.2f (possible packer/encryption)",
				i, section.GetName(), entropy))
		}
	}
}
