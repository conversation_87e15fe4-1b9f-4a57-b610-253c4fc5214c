[tox]
envlist = py38,py39,py310,py311,py312,coverage

[testenv]
deps =
    pytest
    coverage
commands =
    python -m coverage run -p -m pytest --ignore=./tests/pefile_test.py

[testenv:coverage]
basepython = python3.10
commands =
    python -m coverage combine
    python -m coverage report -m --skip-covered --ignore-errors
    python -m coverage json --ignore-errors

[gh-actions]
python =
    3.8: py38
    3.9: py39
    3.10: py310
    3.11: py311
    3.12: py312